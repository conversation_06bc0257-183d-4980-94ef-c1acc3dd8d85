{"info": {"_postman_id": "care-plans-controller-collection", "name": "HMBL Core - Care Plans Controller", "description": "Complete Postman collection for CarePlansController with all routes and example requests", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "memberID", "value": "123e4567-e89b-12d3-a456-426614174000", "type": "string"}, {"key": "carePlanID", "value": "123e4567-e89b-12d3-a456-426614174001", "type": "string"}, {"key": "goalID", "value": "123e4567-e89b-12d3-a456-426614174002", "type": "string"}, {"key": "interventionID", "value": "123e4567-e89b-12d3-a456-426614174003", "type": "string"}, {"key": "problemID", "value": "123e4567-e89b-12d3-a456-426614174004", "type": "string"}, {"key": "teamMemberID", "value": "123e4567-e89b-12d3-a456-426614174005", "type": "string"}, {"key": "reviewID", "value": "123e4567-e89b-12d3-a456-426614174006", "type": "string"}, {"key": "followUpID", "value": "123e4567-e89b-12d3-a456-426614174007", "type": "string"}, {"key": "serviceID", "value": "123e4567-e89b-12d3-a456-426614174008", "type": "string"}], "item": [{"name": "Care Plans", "item": [{"name": "Create Care Plan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"startDate\": \"2025-01-01T00:00:00Z\",\n  \"lastReviewed\": \"2025-01-15T00:00:00Z\",\n  \"nextReviewDate\": \"2025-04-01T00:00:00Z\",\n  \"outcome\": \"Initial assessment completed\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/careplans", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "careplans"]}}}, {"name": "List Care Plans for Member", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/careplans", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "careplans"]}}}, {"name": "Get Care Plan", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}"]}}}, {"name": "Update Care Plan", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"startDate\": \"2025-01-01T00:00:00Z\",\n  \"lastReviewed\": \"2025-01-20T00:00:00Z\",\n  \"nextReviewDate\": \"2025-05-01T00:00:00Z\",\n  \"outcome\": \"Progress noted, goals updated\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}"]}}}, {"name": "Delete Care Plan", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}"]}}}]}, {"name": "Goals", "item": [{"name": "Create Goal", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Improve medication adherence\",\n  \"type\": \"health\",\n  \"targetDate\": \"2025-06-01T00:00:00Z\",\n  \"status\": \"active\",\n  \"outcome\": null,\n  \"objective\": \"Patient will take prescribed medications as directed\",\n  \"measurementCriteria\": \"90% medication adherence rate\",\n  \"achievabilityNote\": \"Patient has shown willingness to improve\",\n  \"barriers\": \"Forgetfulness, complex medication schedule\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/goals", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "goals"]}}}, {"name": "List Goals", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/goals", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "goals"]}}}, {"name": "Update Goal", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Improve medication adherence to 95%\",\n  \"type\": \"health\",\n  \"targetDate\": \"2025-07-01T00:00:00Z\",\n  \"status\": \"in_progress\",\n  \"outcome\": \"Patient showing improvement\",\n  \"objective\": \"Patient will take prescribed medications as directed\",\n  \"measurementCriteria\": \"95% medication adherence rate\",\n  \"achievabilityNote\": \"Patient has shown willingness to improve\",\n  \"barriers\": \"Reduced barriers with pill organizer\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/goals/{{goalID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "goals", "{{goalID}}"]}}}, {"name": "Delete Goal", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/goals/{{goalID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "goals", "{{goalID}}"]}}}]}, {"name": "Interventions", "item": [{"name": "Create Intervention", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"Schedule weekly medication review\",\n  \"responsibleParty\": \"Primary Care Nurse\",\n  \"dueDate\": \"2025-02-01T00:00:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/interventions", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "interventions"]}}}, {"name": "List Interventions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/interventions", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "interventions"]}}}, {"name": "Update Intervention", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"Schedule bi-weekly medication review\",\n  \"responsibleParty\": \"Primary Care Nurse\",\n  \"dueDate\": \"2025-02-15T00:00:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/interventions/{{interventionID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "interventions", "{{interventionID}}"]}}}, {"name": "Delete Intervention", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/interventions/{{interventionID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "interventions", "{{interventionID}}"]}}}]}, {"name": "Problems", "item": [{"name": "Create Problem", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"BP remains elevated despite medication adjustment.\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2023-06-01T00:00:00Z\",\n  \"source\": \"EHR import\",\n  \"confirmedBy\": \"<PERSON><PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "problems"]}}}, {"name": "List Problems", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/problems", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "problems"]}}}, {"name": "Update Problem", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"BP now controlled with current medication regimen. Patient showing good compliance.\",\n  \"status\": \"resolved\",\n  \"dateIdentified\": \"2023-06-01T00:00:00Z\",\n  \"source\": \"EHR import\",\n  \"confirmedBy\": \"Dr<PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/problems/{{problemID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "problems", "{{problemID}}"]}}}, {"name": "Delete Problem", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/problems/{{problemID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "problems", "{{problemID}}"]}}}]}, {"name": "Care Team Members", "item": [{"name": "Create Care Team Member", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userID\": \"123e4567-e89b-12d3-a456-426614174009\",\n  \"name\": \"Dr. <PERSON>\",\n  \"role\": \"Primary Care Physician\",\n  \"contactInfo\": \"<EMAIL>, (555) 123-4567\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/team-members", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "team-members"]}}}, {"name": "List Care Team Members", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/team-members", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "team-members"]}}}, {"name": "Update Care Team Member", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userID\": \"123e4567-e89b-12d3-a456-426614174009\",\n  \"name\": \"Dr. <PERSON>, MD\",\n  \"role\": \"Primary Care Physician\",\n  \"contactInfo\": \"<EMAIL>, (555) 123-4567, ext. 101\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/team-members/{{teamMemberID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "team-members", "{{teamMemberID}}"]}}}, {"name": "Delete Care Team Member", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/team-members/{{teamMemberID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "team-members", "{{teamMemberID}}"]}}}]}, {"name": "Reviews", "item": [{"name": "Create Care Plan Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewDate\": \"2025-01-20T00:00:00Z\",\n  \"notes\": \"Patient showing good progress on medication adherence. Goals remain appropriate.\",\n  \"reviewerName\": \"Dr. <PERSON>\",\n  \"reviewerRole\": \"Primary Care Physician\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/reviews", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "reviews"]}}}, {"name": "List Care Plan Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/reviews", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "reviews"]}}}, {"name": "Update Care Plan Review", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reviewDate\": \"2025-01-20T00:00:00Z\",\n  \"notes\": \"Patient showing excellent progress on medication adherence. Goals updated to reflect new targets.\",\n  \"reviewerName\": \"Dr. <PERSON>\",\n  \"reviewerRole\": \"Primary Care Physician\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/reviews/{{reviewID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "reviews", "{{reviewID}}"]}}}, {"name": "Delete Care Plan Review", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/reviews/{{reviewID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "reviews", "{{reviewID}}"]}}}]}, {"name": "Follow-ups", "item": [{"name": "Create Follow-up", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"datetime\": \"2025-01-25T14:30:00Z\",\n  \"type\": \"Phone\",\n  \"outcome\": \"Reached\",\n  \"notes\": \"Patient confirmed medication adherence improving. Scheduled next appointment.\",\n  \"staffName\": \"Nurse <PERSON>\",\n  \"staffRole\": \"Care Coordinator\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/followups", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "followups"]}}}, {"name": "List Follow-ups", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/followups", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "followups"]}}}, {"name": "Get Follow-up", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/followups/{{followUpID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "followups", "{{followUpID}}"]}}}, {"name": "Update Follow-up", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"datetime\": \"2025-01-25T14:30:00Z\",\n  \"type\": \"Phone\",\n  \"outcome\": \"Reached\",\n  \"notes\": \"Patient confirmed medication adherence at 95%. Very positive progress. Next follow-up in 2 weeks.\",\n  \"staffName\": \"<PERSON>\",\n  \"staffRole\": \"Care Coordinator\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/followups/{{followUpID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "followups", "{{followUpID}}"]}}}, {"name": "Delete Follow-up", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/followups/{{followUpID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "followups", "{{followUpID}}"]}}}]}, {"name": "Services", "item": [{"name": "Create Care Plan Service", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cboName\": \"Community Health Partners\",\n  \"staffName\": \"<PERSON>\",\n  \"addedBy\": \"Dr. <PERSON>\",\n  \"status\": \"pending\",\n  \"appointmentDate\": \"2025-02-05T10:00:00Z\",\n  \"outcomeReasonType\": null,\n  \"outcomeReasonDescription\": null\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/services", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "services"]}}}, {"name": "List Care Plan Services", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/services", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "services"]}}}, {"name": "Get Care Plan Service", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/services/{{serviceID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "services", "{{serviceID}}"]}}}, {"name": "Update Care Plan Service", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cboName\": \"Community Health Partners\",\n  \"staffName\": \"<PERSON>\",\n  \"addedBy\": \"Dr. <PERSON>\",\n  \"status\": \"completed\",\n  \"appointmentDate\": \"2025-02-05T10:00:00Z\",\n  \"outcomeReasonType\": \"successful\",\n  \"outcomeReasonDescription\": \"Patient attended appointment and received nutrition counseling\"\n}"}, "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/services/{{serviceID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "services", "{{serviceID}}"]}}}, {"name": "Delete Care Plan Service", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/careplans/{{carePlanID}}/services/{{serviceID}}", "host": ["{{baseUrl}}"], "path": ["api", "careplans", "{{carePlanID}}", "services", "{{serviceID}}"]}}}]}]}