{"info": {"_postman_id": "member-diagnoses-medications-collection", "name": "HMBL Core - Member Diagnoses & Medications", "description": "Complete Postman collection for Member Diagnoses and Medications controllers", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "memberID", "value": "123e4567-e89b-12d3-a456-426614174000", "type": "string"}, {"key": "diagnosisID", "value": "123e4567-e89b-12d3-a456-426614174001", "type": "string"}, {"key": "medicationID", "value": "123e4567-e89b-12d3-a456-426614174002", "type": "string"}], "item": [{"name": "Member Diagnoses", "item": [{"name": "Create Diagnosis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"BP remains elevated despite medication adjustment. Patient reports compliance with current regimen.\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2023-06-01T00:00:00Z\",\n  \"source\": \"EHR import\",\n  \"confirmedBy\": \"Dr<PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}}}, {"name": "List Member Diagnoses", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}}}, {"name": "Get Diagnosis", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "diagnoses", "{{diagnosisID}}"]}}}, {"name": "Update Diagnosis", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"BP now controlled with current medication regimen. Patient showing excellent compliance.\",\n  \"status\": \"resolved\",\n  \"dateIdentified\": \"2023-06-01T00:00:00Z\",\n  \"source\": \"EHR import\",\n  \"confirmedBy\": \"Dr<PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "diagnoses", "{{diagnosisID}}"]}}}, {"name": "Delete Diagnosis", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "diagnoses", "{{diagnosisID}}"]}}}]}, {"name": "Member Medications", "item": [{"name": "Create Medication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"medicationName\": \"Lisinopril 10mg\",\n  \"rxNormCode\": \"197361\",\n  \"dosage\": \"10mg\",\n  \"route\": \"oral\",\n  \"frequency\": \"daily\",\n  \"startDate\": \"2023-05-20T00:00:00Z\",\n  \"prescribedBy\": \"<PERSON><PERSON> <PERSON>\",\n  \"status\": \"active\",\n  \"adherenceNotes\": \"Member reports 90% adherence\",\n  \"source\": \"EHR import\",\n  \"medicationType\": \"prescribed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}}}, {"name": "List Member Medications", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}}}, {"name": "Get Active Medications", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/active", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "active"]}}}, {"name": "Get Medications by Type", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/by-type?type=prescribed", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "by-type"], "query": [{"key": "type", "value": "prescribed"}]}}}, {"name": "Get Medication", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "medications", "{{medicationID}}"]}}}, {"name": "Update Medication", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"medicationName\": \"Lisinopril 20mg\",\n  \"rxNormCode\": \"197361\",\n  \"dosage\": \"20mg\",\n  \"route\": \"oral\",\n  \"frequency\": \"daily\",\n  \"startDate\": \"2023-05-20T00:00:00Z\",\n  \"prescribedBy\": \"<PERSON><PERSON> <PERSON>\",\n  \"status\": \"active\",\n  \"adherenceNotes\": \"Dosage increased. Member reports 95% adherence\",\n  \"source\": \"EHR import\",\n  \"medicationType\": \"prescribed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "medications", "{{medicationID}}"]}}}, {"name": "Discontinue Medication", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/{{medicationID}}/discontinue", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "{{medicationID}}", "discontinue"]}}}, {"name": "Delete Medication", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "medications", "{{medicationID}}"]}}}]}]}