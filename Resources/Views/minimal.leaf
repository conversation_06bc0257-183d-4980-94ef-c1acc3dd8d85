<!DOCTYPE html>
<html>
<head>
    <link href="https://fonts.cdnfonts.com/css/graphik" rel="stylesheet">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.2/Chart.bundle.min.js"></script>
  <title>Inline HTML Code</title>
   <style>
   html, body {
       max-height: 400px;
       max-width: 500px;
    }
    
    body {
      font-family: 'Graphik', sans-serif;
    }
    
    .top-cell {
       display: flex;
    }
                
    table {
      border-collapse: collapse;
    }

    .score-header {
        font-family: 'Graphik';
        font-style: normal;
        font-weight: 500;
        font-size: 27px;
        line-height: 100%;
        color: #1470C4;
        padding-top:0;
    }
    
    .min-header {
        font-family: 'Graphik';
        font-style: normal;
        font-weight: 500;
        font-size: 19px;
        line-height: 100%;
        color: #001018;
    }
    
    .name {
        font-family: 'Graphik';
        font-style: normal;
        font-weight: 500;
        font-size: 27px;
        line-height: 100%;
    }
    
    th, td {
      border-bottom: 1px solid black;
      border-color: #D5DCE2;
      padding-bottom: 15px;
      padding-top: 15px;
      padding-left: 18px;
    }
    
    table {
       padding-top: 0px;
       padding-bottom: 0px;
    }
    
    td {
        font-family: 'Graphik';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 100%;
        color: #646F79;
    }
    
    
    
    .value {
        font-family: 'Graphik';
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 100%;
        color: #001018;
        padding-left: 32px;
    }
    
    .circle-image {
      border-radius: 50%;
      width: 215px;
      height:215px;
    }
    
    tr:not(:last-child) {
      border-bottom: 1px solid black;
    }
    
    .stacked-items {
        padding-left: 25px;
        line-height: 13px;
    }
    
    .stacked-labels {
        font-size: 25px;
    }
    
    
    .stacked-title {
        font-size: 25px;
        font-style: normal;
        font-weight: 700;
        font-size: 30px;
        color: #001018;
    }
    .score {
        font-family: 'Helvetica';
        font-style: normal;
        font-weight: 700;
        font-size: 30px;
        color: #1470C4;
    }
    
    .image-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr); /* Adjust the number of columns as needed */
      grid-gap: 10px; /* Adjust the gap between images as needed */
    }

    .image-grid img {
      width: 100%;
      height: auto;
    }
    
    .image-item p {
      margin-top: 10px; /* Adjust the spacing between the image and text as needed */
      text-align: center;
    }

    .divider {
            height:1px;
            width:100%;
            background:#DDDDDD;
            margin-top:15px;
            margin-bottom:15px;
        }

  </style>

</head>
<body>
    <div class ="top-cell">
     <img class="circle-image" src=#(memberURL) alt="Circular Image">
     <div class="stacked-items">
     <p class="stacked-title">#(dashboard.member.firstName) #(dashboard.member.lastName)</p>
     <p class="stacked-labels">#(dashboard.member.gender)  |  32 yrs old</p>
     <p class="stacked-labels">12345 SW 67 Street,</p>
     <p class="stacked-labels">Miami, FL 33123</p>
     <p class="score">Safety Net</p>
     </div>
  </div>
  <div class="divider"></div>
  <div class="image-grid">
        <div class="image-item">
            <img src="/images/income.png" alt="Image 1">
            <p>Income</p>
        </div>

        <div class="image-item">
            <img src="/images/nutrition.png" alt="Image 2">
            <p>Food</p>
        </div>

        <div class="image-item">
            <img src="/images/house.png" alt="Image 3">
            <p>Housing</p>
        </div>

        <div class="image-item">
            <img src="/images/tech.png" alt="Image 4">
            <p>Tech</p>
        </div>

        <div class="image-item">
            <img src="/images/trans.png" alt="Image 1">
            <p>Rides</p>
        </div>

        <div class="image-item">
            <img src="/images/policy.png" alt="Image 2">
            <p>Policy</p>
        </div>

        <div class="image-item">
            <img src="/images/employ.png" alt="Image 3">
            <p>Jobs</p>
        </div>

        <div class="image-item">
            <img src="/images/edu.png" alt="Image 4">
            <p>School</p>
        </div>
  </div>
</body>
</html>
