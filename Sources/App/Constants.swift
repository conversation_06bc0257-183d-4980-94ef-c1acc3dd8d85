//
//  File.swift
//
//
//  Created by <PERSON> on 2/7/23.
//

import Foundation
import Vapor


// Enum for PlanType
enum PlanCoverage: String, Codable {
    case family = "family"
    case individual = "individual"
}

enum PlanType: String, Codable {
    case hmo = "hmo"
    case ppo = "ppo"
    case epo = "epo"
    case pos = "pos"
    case hdhp = "hdhp"
    case hsa = "hsa"
    case catastrophic = "catastrophic"
    case medicare = "medicare"
    case medicaid = "medicaid"
    case shortTerm = "short term"
}

enum Accesskeys: String, Codable {
    case tasks, chats, households, navigators, reporting, teams, networks, members
    
    static let `default`: [Accesskeys] = [.tasks, .chats, .households, .navigators, .reporting, .teams, .networks, .members]
}

enum ServiceType: String, Codable {
    case all, rehabilitation, housing, employment, mental, physical, education, skills, income, nutrition, transportation, food, technology, health, legal, multi, text, select_one, policy, single, none, multiline, treatment
}

enum RolesTypes: String, Codable {
    case support, member, lead
}

enum PackageType: String, Codable {
    case individual, household
}

enum StaffStatus: String, Codable {
    case active, inactive, pending
}

enum Status: String, Codable {
    case accepting, notaccepting
}

enum SurveyType: String, Codable {
    case rapid_hrp, full_hrp, prapare, ahc_hrsn
}

enum TimelineStatus: String, Codable {
    case pending, booked, removed, complete, ended, noShow, cancel
}

enum CarePackageSections: String, Codable {
    case income,
         nutrition,
         housing,
         policy_determinants,
         technology,
         transportation,
         employment,
         life_skills_education,
         policy,
         health,
         mental,
         physical
}

struct PickerItem:Codable {
    var title:String
    var key:String
    var color:String? = nil
}


struct ScoreItem:Codable {
    var question_score:Int
    var overall_score_range:[Int]
    var title:String
    var color:String
}

struct WhiteLabel: Codable {
    var buttonColor:     String?
    var disabled:        String?
    var black:           String?
    var orange:          String?
    var viewBackground:  String?
    var background:      String?
    var green:           String?
    var blue:            String?
    var gray:            String?
    var unselectedGray:  String?
    var border:          String?
    var lightGreen:      String?
    var icon:            String?
    var domain:          String
}

struct WhitelabelsConstants: Content {
    var domains:[WhiteLabel] = [
        WhiteLabel(
            buttonColor:"#FFC828",
            green: "#FFC828",
            lightGreen: "#FFEFC0",
            icon:"https://res.cloudinary.com/cg1-solutions/image/upload/v1685409491/donahealth/whitelabel/vilage_umpxt0.png",
            domain: "villagemd.com"),
        WhiteLabel(
            buttonColor:"#5C0127",
            green: "#5C0127",
            lightGreen: "#593A47",
            icon:"https://res.cloudinary.com/cg1-solutions/image/upload/v1685486441/donahealth/whitelabel/rosemand_so7dss.png",
            domain: "roseman.edu"),
    ]
}

struct ApplicationConstants: Content {
    
    var appointmentKinds: [PickerItem] = [
        PickerItem(title: "virtual", key: "virtual"),
        PickerItem(title: "appointment", key: "appointment")
    ]
    
    var appointmentStatus: [PickerItem] = [
        PickerItem(title: "booked", key: "booked"),
        PickerItem(title: "complete", key: "complete"),
        PickerItem(title: "in session", key: "in_session"),
        PickerItem(title: "canceled", key: "canceled"),
    ]
    
    
    var staffStatus: [PickerItem]      = [
        PickerItem(title: "Active", key:StaffStatus.active.rawValue),
        PickerItem(title: "In Active", key:"in_active_withdrawn"),
        PickerItem(title: "Pending", key:StaffStatus.pending.rawValue),
        PickerItem(title: "New referral", key: "new_referral"),
        PickerItem(title: "Awaiting Enrollment ", key: "awaiting_enrollment"),
        PickerItem(title: "On Hold", key: "on_hold"),
        PickerItem(title: "Not Ready", key: "not_ready"),
        PickerItem(title: "Household Member", key: "household_member"),
        PickerItem(title: "Unable to reach", key: "unable_to_reach"),
        PickerItem(title: "Static", key: "static"),
        PickerItem(title: "Closed", key: "closed_withdrawn"),
        PickerItem(title: "Virtual only - Local", key: "virtual_only_local"),
        PickerItem(title: "Graduate - Resilient", key: "graduate_resilient_withdrawn"),
        PickerItem(title: "Virtual Only - Relocated", key: "virtual_only_relocated"),
    ]
    
    var networkStatus: [PickerItem]         = [
        PickerItem(title: "Accepting", key: Status.accepting.rawValue),
        PickerItem(title: "Not Accepting", key: Status.notaccepting.rawValue)]
    var roleTypes: [RolesTypes]         = [.support, .member, .lead]
    var surveyTypes: [PickerItem]       = []
    
    var timelineStatus:[PickerItem] = [
        PickerItem(title: "Pending",              key: TimelineStatus.pending.rawValue),
        PickerItem(title: "Appointment Booked",   key: TimelineStatus.booked.rawValue),
        PickerItem(title: "Appointment Complete", key: TimelineStatus.complete.rawValue),
        PickerItem(title: "Appointment Cancel",   key: TimelineStatus.cancel.rawValue),
        PickerItem(title: "No Show",              key: TimelineStatus.noShow.rawValue),
        PickerItem(title: "Network Removed",      key: TimelineStatus.removed.rawValue),
    ]
    
    var pregnancyStatus: [PickerItem] = [
        PickerItem(title: "Not Pregnant", key: "not_pregnant"),
        PickerItem(title: "Pregnant", key: "pregnant"),
        PickerItem(title: "Delivered", key: "delivered"),
        PickerItem(title: "Postpartum", key: "postpartum"),
        PickerItem(title: "Miscarriage", key: "miscarriage")
    ]
    
    var networkServices: [PickerItem]  = [
        PickerItem(title:"education", key:ServiceType.education.rawValue),
        PickerItem(title:"employment",key:ServiceType.employment.rawValue),
        PickerItem(title:"housing",key:ServiceType.housing.rawValue),
        PickerItem(title:"income",key:ServiceType.income.rawValue),
        PickerItem(title:"mental",key:ServiceType.mental.rawValue),
        PickerItem(title:"physical",key:ServiceType.physical.rawValue),
        PickerItem(title:"rehabilitation",key:ServiceType.rehabilitation.rawValue),
        PickerItem(title:"skills",key:ServiceType.skills.rawValue),
        PickerItem(title:"nutrition",key:ServiceType.nutrition.rawValue),
        PickerItem(title:"transportation",key:ServiceType.transportation.rawValue),
        PickerItem(title:"food",key:ServiceType.food.rawValue),
        PickerItem(title:"technology",key:ServiceType.technology.rawValue),
        PickerItem(title:"health",key:ServiceType.health.rawValue),
        PickerItem(title:"legal",key:ServiceType.legal.rawValue),
        PickerItem(title:"treatments", key:"treatments"),
        PickerItem(title:"programs", key:"programs"),
        PickerItem(title:"insurance", key:"insurance"),
        PickerItem(title:"legal", key:"legal"),
        PickerItem(title:"community organization", key:"community_organization"),
        PickerItem(title:"family", key:"family")
    ]
    
    var carePackageSections: [PickerItem] = [
        PickerItem(title: "income", key: CarePackageSections.income.rawValue),
        PickerItem(title: "nutrition", key: CarePackageSections.nutrition.rawValue),
        PickerItem(title: "housing", key: CarePackageSections.housing.rawValue),
        PickerItem(title: "policy determinants", key: CarePackageSections.policy_determinants.rawValue),
        PickerItem(title: "technology", key: CarePackageSections.technology.rawValue),
        PickerItem(title: "transportation", key: CarePackageSections.transportation.rawValue),
        PickerItem(title: "employment", key: CarePackageSections.employment.rawValue),
        PickerItem(title: "life skills & education", key: CarePackageSections.life_skills_education.rawValue),
        PickerItem(title: "Healthcare", key: CarePackageSections.health.rawValue),
        PickerItem(title: "Mental", key: CarePackageSections.mental.rawValue),
        PickerItem(title: "Physical", key: CarePackageSections.physical.rawValue)]
    
    var reasons = [
        PickerItem(title: "Member Received Sufficient Help", key: "helped"),
        PickerItem(title: "Member Opted Out", key: "opt_out"),
        PickerItem(title: "Member No Longer Needs Service", key: "no_longer"),
        PickerItem(title: "Created unintentionally", key: "nav_error"),
        PickerItem(title: "Relocated outside of grant perimeters", key: "relocated_outside"),
        PickerItem(title: "Loss of contact", key: "loss_of_contact"),
        PickerItem(title: "Program Termination", key: "program_termination")
    ]
    
    var memberTypes: [PickerItem]  = [
        PickerItem(title: "Person - Youth (0-17yrs)", key: "youth"),
        PickerItem(title: "Person - Adult (18-64yrs)", key: "adult"),
        PickerItem(title: "Senior", key: "senior"),
        PickerItem(title: "Pet", key: "pet")]
    
    var carepackageTypes:[PickerItem] = [
        PickerItem(title: "Individual", key: PackageType.individual.rawValue),
        PickerItem(title: "Household",  key: PackageType.household.rawValue),
    ]
    
    var relationship: [PickerItem] = [
        PickerItem(title:"Parent", key: "parent"),
        PickerItem(title:"Child", key: "child"),
        PickerItem(title:"Sibling", key: "sibling"),
        PickerItem(title:"Spouse", key: "spouse"),
        PickerItem(title:"Partner", key: "partner"),
        PickerItem(title:"Grandparent", key: "grandparent"),
        PickerItem(title:"Grandchild", key: "grandchild"),
        PickerItem(title:"Aunt/Uncle", key: "aunt_uncle"),
        PickerItem(title:"Niece/Nephew", key: "niece_nephew"),
        PickerItem(title:"Cousin", key: "cousin"),
        PickerItem(title:"Friend", key: "friend"),
        PickerItem(title:"Colleague", key: "colleague"),
        PickerItem(title:"Neighbor", key: "neighbor"),
        PickerItem(title:"Guardian", key: "guardian"),
        PickerItem(title:"Ward", key: "ward"),
        PickerItem(title:"Mentor", key: "mentor"),
        PickerItem(title:"Mentee", key: "mentee"),
        PickerItem(title:"In-Law", key: "in_law"),
        PickerItem(title:"Step-Parent", key: "step_parent"),
        PickerItem(title:"Step-Child", key:"step_child")
    ]
    
    var attachmentTypes:[PickerItem] = [
        PickerItem(title: "Profile", key: "profile"),
        PickerItem(title: "Attachment",  key: "attachment"),
        PickerItem(title: "Encounter",  key: "encounter"),
        PickerItem(title: "Insurance",  key: "insurance"),
    ]
    
    var planCoverage:[PickerItem] = [
        PickerItem(title: "individual", key:PlanCoverage.individual.rawValue),
        PickerItem(title: "family", key:PlanCoverage.family.rawValue),
    ]
    
    var medicareParts:[PickerItem] = [
        PickerItem(title: "Part A", key: "part_a"),
        PickerItem(title: "Part B", key: "part_b"),
        PickerItem(title: "Part C", key: "part_c"),
        PickerItem(title: "Part D", key: "part_d")
    ]
    
    var planTypes:[PickerItem] = [
        PickerItem(title: "medicare", key:PlanType.medicare.rawValue),
        PickerItem(title: "medicaid", key:PlanType.medicaid.rawValue),
        PickerItem(title: "hmo", key:PlanType.hmo.rawValue),
        PickerItem(title: "ppo", key:PlanType.ppo.rawValue),
        PickerItem(title: "epo", key:PlanType.epo.rawValue),
        PickerItem(title: "pos", key:PlanType.pos.rawValue),
        PickerItem(title: "hdhp", key:PlanType.hdhp.rawValue),
        PickerItem(title: "hsa", key:PlanType.hsa.rawValue),
        PickerItem(title: "catastrophic", key:PlanType.catastrophic.rawValue),
        PickerItem(title: "short term", key:PlanType.shortTerm.rawValue)
    ]
    
    var consentLangs = ["en-us", "es"]
    
    var uploadTypes =  [
        "user", "profile", "encounter", "attachment", "household", "pet", "userProfile", "householdProfile", "card"
    ]
    
    var taskTypes:[PickerItem] =  [
        PickerItem(title: "assessment", key: "assessment"),
        PickerItem(title: "contact", key: "contact"),
        PickerItem(title: "visit", key: "visit"),
        PickerItem(title: "custom", key: "custom")
    ]
    
    var contactTaskTypes:[PickerItem] =  [
        PickerItem(title: "Phone Call", key: "phone"),
        PickerItem(title: "Chat Message", key: "message"),
        PickerItem(title: "Televisit", key: "televisit"),
        PickerItem(title: "any method", key: "any")
    ]
    
    var appointmentTypes: [PickerItem] = [
        PickerItem(title: "Televisit", key: "televisit"),
        PickerItem(title: "In-Person Visit", key: "in_person"),
        PickerItem(title: "Follow-up", key: "follow_up"),
        PickerItem(title: "Initial Consultation", key: "initial_consultation"),
        PickerItem(title: "Behavioral Health", key: "behavioral_health"),
        PickerItem(title: "Psychiatry", key: "psychiatry"),
        PickerItem(title: "Therapy Session", key: "therapy"),
        PickerItem(title: "Group Session", key: "group_session"),
        PickerItem(title: "Medication Review", key: "medication_review"),
        PickerItem(title: "Case Management", key: "case_management")
    ]
    
    var taskCompletionTypes:[PickerItem] =  [
//        PickerItem(title: "No End Date", key: "no_end_date"),
        PickerItem(title: "Completed by Certain Date", key: "datetime"),
        PickerItem(title: "Completed at Specific Time", key: "datetime")
    ]
    
    var taskCompletionReasons:[PickerItem] =  [
        PickerItem(title: "Completed Task", key: "completed"),
        PickerItem(title: "Task incomplete", key: "incomplete"),
        PickerItem(title: "Other", key: "other")
    ]
    
    var militaryStatus: [PickerItem] = [
        PickerItem(title:"Veteran", key:"veteran"),
        PickerItem(title:"Active Duty", key:"active_duty"),
        PickerItem(title:"Reserve", key:"reserve"),
        PickerItem(title:"National Guard", key:"national_guard"),
        PickerItem(title:"Retired Veteran", key:"retired"),
        PickerItem(title:"Disabled Veteran", key:"disabled"),
        PickerItem(title:"Combat Veteran", key:"combat"),
        PickerItem(title:"Non Veteran", key:"non_veteran"),
        PickerItem(title:"Prefer not to say", key: "prefer_not_to_say")
    ]
    
    var uploadKinds:[PickerItem] =  [
        PickerItem(title: "Profile", key: "userProfile"),
        PickerItem(title: "Pet", key: "pet"),
        PickerItem(title: "Attachment", key: "attachment"),
        PickerItem(title: "Document", key: "document"),
        PickerItem(title: "House", key: "householdProfile"),
        PickerItem(title: "Other", key: "other")
    ]
    
    var rating:[PickerItem] =  [
        PickerItem(title: "Excellent", key: "excellent"),
        PickerItem(title: "Mediocre", key: "mediocre"),
        PickerItem(title: "Poor", key: "poor")
    ]
    
    var memberTags: [PickerItem] = [
        // 🏠 Housing & Shelter
        PickerItem(title: "Homeless", key: "homeless"),
        PickerItem(title: "Housing Insecurity", key: "housing_insecurity"),
        PickerItem(title: "Eviction Risk", key: "eviction_risk"),
        PickerItem(title: "Transitional Housing", key: "transitional_housing"),
        PickerItem(title: "Rent Assistance", key: "rent_assistance"),
        PickerItem(title: "Emergency Shelter", key: "emergency_shelter"),

        // 🍽️ Food Security
        PickerItem(title: "Food Insecurity", key: "food_insecurity"),
        PickerItem(title: "SNAP Recipient", key: "snap_recipient"),
        PickerItem(title: "Food Pantry Assistance", key: "food_pantry_assistance"),
        PickerItem(title: "Free Meals Program", key: "free_meals_program"),
        PickerItem(title: "WIC Participant", key: "wic_participant"),

        // 🚗 Transportation
        PickerItem(title: "No Personal Vehicle", key: "no_personal_vehicle"),
        PickerItem(title: "Public Transport Dependent", key: "public_transport_dependent"),
        PickerItem(title: "Transportation Assistance", key: "transportation_assistance"),
        PickerItem(title: "Medical Transport Needed", key: "medical_transport_needed"),

        // 💼 Employment & Income
        PickerItem(title: "Unemployed", key: "unemployed"),
        PickerItem(title: "Low-Income", key: "low_income"),
        PickerItem(title: "Job Training Needed", key: "job_training_needed"),
        PickerItem(title: "Financial Assistance", key: "financial_assistance"),
        PickerItem(title: "Workforce Development", key: "workforce_development"),

        // 🏥 Healthcare Access
        PickerItem(title: "Uninsured", key: "uninsured"),
        PickerItem(title: "Medicaid Recipient", key: "medicaid_recipient"),
        PickerItem(title: "Chronic Condition", key: "chronic_condition"),
        PickerItem(title: "Mental Health Support", key: "mental_health_support"),
        PickerItem(title: "Substance Use Recovery", key: "substance_use_recovery"),
        PickerItem(title: "Healthcare Navigation", key: "healthcare_navigation"),

        // 🛡️ Safety & Environment
        PickerItem(title: "Unsafe Housing", key: "unsafe_housing"),
        PickerItem(title: "Domestic Violence Survivor", key: "domestic_violence_survivor"),
        PickerItem(title: "Community Safety Concern", key: "community_safety_concern"),
        PickerItem(title: "Legal Aid Needed", key: "legal_aid_needed"),

        // 📚 Education & Childcare
        PickerItem(title: "GED/Adult Education", key: "ged_adult_education"),
        PickerItem(title: "Childcare Assistance", key: "childcare_assistance"),
        PickerItem(title: "School Lunch Program", key: "school_lunch_program"),
        PickerItem(title: "After-School Program", key: "after_school_program"),

        // 🔗 General Support & Referrals
        PickerItem(title: "Case Management", key: "case_management"),
        PickerItem(title: "Resource Navigation", key: "resource_navigation"),
        PickerItem(title: "Peer Support Program", key: "peer_support_program"),
        PickerItem(title: "Emergency Assistance", key: "emergency_assistance")
    ]
    
    var roles:[PickerItem] =  [
        PickerItem(title: "Member", key: "role_member_member"),
        PickerItem(title: "Team Lead", key: "role_admin_lead"),
        PickerItem(title: "Scocial Worker", key: "role_viewer_social"),
        PickerItem(title: "Community Rep", key: "role_viewer_rep"),
        PickerItem(title: "Navigator", key: "role_admin_nav"),
        PickerItem(title: "Administrator", key: "role_admin"),
        PickerItem(title: "Community Health Worker", key: "role_admin_worker"),
        PickerItem(title: "Operations Manager", key: "role_admin_manager"),
        PickerItem(title: "LCSW", key: "role_admin_lcsw"),
        PickerItem(title: "Peer", key: "role_admin_peer"),
        PickerItem(title: "Program Care Manager", key: "role_admin_care"),
        PickerItem(title: "Coordinator", key: "role_admin_coordinator")
        
    ]
    
    var sexualIdentities:[PickerItem] = [PickerItem(title: "Heterosexual (Straight)", key: "heterosexual_straight"),
                                         PickerItem(title: "Homosexual (Gay/Lesbian)", key: "homosexual_gay_lesbian)"),
                                         PickerItem(title: "Bisexual", key: "bisexual"),
                                         PickerItem(title: "Pansexual", key: "pansexual"),
                                         PickerItem(title: "Asexual", key: "asexual"),
                                         PickerItem(title: "Demisexual", key: "demisexual"),
                                         PickerItem(title: "Queer", key: "queer"),
                                         PickerItem(title: "Questioning", key: "questioning"),
                                         PickerItem(title: "Fluid", key: "fluid"),
                                         PickerItem(title: "Polysexual", key: "polysexual"),
                                         PickerItem(title: "Skoliosexual", key: "skoliosexual"),
                                         PickerItem(title: "Androsexual", key: "androsexual"),
                                         PickerItem(title: "Gynesexual", key: "gynesexual"),
                                         PickerItem(title: "Sapiosexual", key: "sapiosexual"),
                                         PickerItem(title: "Prefer not to say", key: "prefer_not_to_say"),
                                         PickerItem(title: "other", key: "other")]
    
    var pronouns =  [
        PickerItem(title: "She, Her, Hers", key:"she_her_hers"),
        PickerItem(title: "He, Him, His", key:"he_him_his"),
        PickerItem(title: "They, Them, Theirs", key:"they_them_theirs"),
        PickerItem(title: "Ze, Hir, Hirs", key:"ze_hir_hirs"),
        PickerItem(title: "Ze, Zir, Zirs", key:"ze_zir_zirs"),
        PickerItem(title: "Xe, Xem, Xyrs", key:"xe_xem_xyrs"),
        PickerItem(title: "Per, Per, Pers", key:"per_per_pers"),
        PickerItem(title: "Ve, Ver, Vers", key:"ve_ver_vers"),
        PickerItem(title: "E, Ey, Em", key:"e_ey_em"),
        PickerItem(title: "Ey, Em, Eirs", key:"ey_em_eirs"),
        PickerItem(title: "Ne, Nem, Nirs", key:"ne_nem_nirs"),
        PickerItem(title: "Spivak (Ey, Em, Eir)", key:"spivak_ey_em_eir"),
        PickerItem(title: "Singular They, Them, Theirs", key:"singular_they_them_theirs"),
        PickerItem(title: "Name Only (no pronouns)", key:"name_only__no_pronouns"),
        PickerItem(title: "Prefer not to say", key:"prefer_not_to_say")
    ]
    
    var genderIndentity:[PickerItem] = [
        PickerItem(title:"Male", key:"male"),
        PickerItem(title:"Female", key:"female"),
        PickerItem(title:"Non-binary", key:"non_binary"),
        PickerItem(title:"Genderqueer", key:"denderqueer"),
        PickerItem(title:"Agender", key:"agender"),
        PickerItem(title:"Genderfluid", key:"genderfluid"),
        PickerItem(title:"Two-spirit", key:"two_spirit"),
        PickerItem(title:"Transgender", key:"transgender"),
        PickerItem(title:"Gender nonconforming", key:"gender_nonconforming"),
        PickerItem(title:"Bigender", key:"bigender"),
        PickerItem(title:"Androgynous", key:"androgynous"),
        PickerItem(title:"other gender identity", key:"other"),
        PickerItem(title:"Prefer not to say", key:"prefer_not_to_say")
    ]
    
    var hospitalization:[PickerItem] =  [
        PickerItem(title:"Admitted", key:"admitted"),
        PickerItem(title:"Discharged", key:"discharged")
    ]
    
    var homeless:[PickerItem] =  [
        PickerItem(title:"Homeless Shelter", key:"homeless_shelter"),
        PickerItem(title:"Mobile Living", key:"mobile_living"),
        PickerItem(title:"On The Streets", key:"homeless_streets")
    ]
    
    var notesSubcategory:[PickerItem] =  [
        PickerItem(title: "Basic Note", key: "basic_note"),
        PickerItem(title: "Intake", key:"intake"),
        PickerItem(title: "Routine patient follow up", key:"routine_patient_follow_up"),
        PickerItem(title: "Individual peer session", key:"individual_peer_session"),
        PickerItem(title: "Peer support group", key:"support_group"),
        PickerItem(title: "Peer navigation", key:"peer_navigation"),
        PickerItem(title: "Peer accompaniment", key:"peer_accompaniment"),
        PickerItem(title: "Case management", key:"case_management"),
        PickerItem(title: "Crisis session", key:"crisis_session"),
        PickerItem(title: "Mandated reporting", key:"mandated_reporting"),
        PickerItem(title: "Sober Social Event", key:"sober_social_event"),
        PickerItem(title: "COSP", key:"cosp"),
        PickerItem(title: "Anger Management", key:"anger_management")
    ]
    
    var noteTags:[PickerItem] = [
        PickerItem(title: "general", key: "general", color: "0ABF89"),
        PickerItem(title: "In Office", key: "in_office", color: "B74F00"),
        PickerItem(title: "In the community", key: "in_the_community", color: "0ABF89"),
        PickerItem(title: "house visit", key: "house_visit", color: "E97100"),
        PickerItem(title: "Phone/Email/Text", key: "phone_email_text", color: "E97100"),
        PickerItem(title: "Home visit", key: "home_visit", color: "27AAE1"),
        PickerItem(title: "Virtual", key: "virtual", color: "0ABF89"),
        PickerItem(title: "home scan", key: "home_scan", color: "1470C4"),
        PickerItem(title: "communications", key: "communications", color: "0DAAE4"),
        PickerItem(title: "images", key: "images", color: "DE6F09"),
        PickerItem(title: "basic", key: "basic", color: "8996A2"),
        PickerItem(title: "urgent", key: "urgent", color: "FE3B2F"),
        PickerItem(title: "next steps", key: "next_step", color: "0DAAE4")
    ]
    
    var nationalities:[PickerItem] = [
        PickerItem(title:"Afghan", key:"Afghan"),
        PickerItem(title:"Albanian", key:"Albanian"),
        PickerItem(title:"Algerian", key:"Algerian"),
        PickerItem(title:"American", key:"American"),
        PickerItem(title:"Andorran", key:"Andorran"),
        PickerItem(title:"Angolan", key:"Angolan"),
        PickerItem(title:"Antiguans", key:"Antiguans"),
        PickerItem(title:"Argentinean", key:"Argentinean"),
        PickerItem(title:"Armenian", key:"Armenian"),
        PickerItem(title:"Australian", key:"Australian"),
        PickerItem(title:"Austrian", key:"Austrian"),
        PickerItem(title:"Azerbaijani", key:"Azerbaijani"),
        PickerItem(title:"Bahamian", key:"Bahamian"),
        PickerItem(title:"Bahraini", key:"Bahraini"),
        PickerItem(title:"Bangladeshi", key:"Bangladeshi"),
        PickerItem(title:"Barbadian", key:"Barbadian"),
        PickerItem(title:"Barbudans", key:"Barbudans"),
        PickerItem(title:"Batswana", key:"Batswana"),
        PickerItem(title:"Belarusian", key:"Belarusian"),
        PickerItem(title:"Belgian", key:"Belgian"),
        PickerItem(title:"Belizean", key:"Belizean"),
        PickerItem(title:"Beninese", key:"Beninese"),
        PickerItem(title:"Bhutanese", key:"Bhutanese"),
        PickerItem(title:"Bolivian", key:"Bolivian"),
        PickerItem(title:"Bosnian", key:"Bosnian"),
        PickerItem(title:"Brazilian", key:"Brazilian"),
        PickerItem(title:"British", key:"British"),
        PickerItem(title:"Bruneian", key:"Bruneian"),
        PickerItem(title:"Bulgarian", key:"Bulgarian"),
        PickerItem(title:"Burkinabe", key:"Burkinabe"),
        PickerItem(title:"Burmese", key:"Burmese"),
        PickerItem(title:"Burundian", key:"Burundian"),
        PickerItem(title:"Cambodian", key:"Cambodian"),
        PickerItem(title:"Cameroonian", key:"Cameroonian"),
        PickerItem(title:"Canadian", key:"Canadian"),
        PickerItem(title:"Cape Verdean", key:"Cape Verdean"),
        PickerItem(title:"Central African", key:"Central African"),
        PickerItem(title:"Chadian", key:"Chadian"),
        PickerItem(title:"Chilean", key:"Chilean"),
        PickerItem(title:"Chinese", key:"Chinese"),
        PickerItem(title:"Colombian", key:"Colombian"),
        PickerItem(title:"Comoran", key:"Comoran"),
        PickerItem(title:"Congolese", key:"Congolese"),
        PickerItem(title:"Congolese", key:"Congolese"),
        PickerItem(title:"Costa Rican", key:"Costa Rican"),
        PickerItem(title:"Croatian", key:"Croatian"),
        PickerItem(title:"Cuban", key:"Cuban"),
        PickerItem(title:"Cypriot", key:"Cypriot"),
        PickerItem(title:"Czech", key:"Czech"),
        PickerItem(title:"Danish", key:"Danish"),
        PickerItem(title:"Djibouti", key:"Djibouti"),
        PickerItem(title:"Dominican", key:"Dominican"),
        PickerItem(title:"Dominican", key:"Dominican"),
        PickerItem(title:"Dutch", key:"Dutch"),
        PickerItem(title:"Dutchman", key:"Dutchman"),
        PickerItem(title:"Dutchwoman", key:"Dutchwoman"),
        PickerItem(title:"East Timorese", key:"East Timorese"),
        PickerItem(title:"Ecuadorean", key:"Ecuadorean"),
        PickerItem(title:"Egyptian", key:"Egyptian"),
        PickerItem(title:"Emirian", key:"Emirian"),
        PickerItem(title:"Equatorial Guinean", key:"Equatorial Guinean"),
        PickerItem(title:"Eritrean", key:"Eritrean"),
        PickerItem(title:"Estonian", key:"Estonian"),
        PickerItem(title:"Ethiopian", key:"Ethiopian"),
        PickerItem(title:"Fijian", key:"Fijian"),
        PickerItem(title:"Tagalog", key:"Tagalog"),
        PickerItem(title:"Finnish", key:"Finnish"),
        PickerItem(title:"French", key:"French"),
        PickerItem(title:"Gabonese", key:"Gabonese"),
        PickerItem(title:"Gambian", key:"Gambian"),
        PickerItem(title:"Georgian", key:"Georgian"),
        PickerItem(title:"German", key:"German"),
        PickerItem(title:"Ghanaian", key:"Ghanaian"),
        PickerItem(title:"Greek", key:"Greek"),
        PickerItem(title:"Grenadian", key:"Grenadian"),
        PickerItem(title:"Guatemalan", key:"Guatemalan"),
        PickerItem(title:"Guinea-Bissauan", key:"Guinea-Bissauan"),
        PickerItem(title:"Guinean", key:"Guinean"),
        PickerItem(title:"Guyanese", key:"Guyanese"),
        PickerItem(title:"Haitian", key:"Haitian"),
        PickerItem(title:"Herzegovinian", key:"Herzegovinian"),
        PickerItem(title:"Honduran", key:"Honduran"),
        PickerItem(title:"Hungarian", key:"Hungarian"),
        PickerItem(title:"I-Kiribati", key:"I-Kiribati"),
        PickerItem(title:"Icelander", key:"Icelander"),
        PickerItem(title:"Indian", key:"Indian"),
        PickerItem(title:"Indonesian", key:"Indonesian"),
        PickerItem(title:"Iranian", key:"Iranian"),
        PickerItem(title:"Iraqi", key:"Iraqi"),
        PickerItem(title:"Irish", key:"Irish"),
        PickerItem(title:"Irish", key:"Irish"),
        PickerItem(title:"Israeli", key:"Israeli"),
        PickerItem(title:"Italian", key:"Italian"),
        PickerItem(title:"Ivorian", key:"Ivorian"),
        PickerItem(title:"Jamaican", key:"Jamaican"),
        PickerItem(title:"Japanese", key:"Japanese"),
        PickerItem(title:"Jordanian", key:"Jordanian"),
        PickerItem(title:"Kazakhstani", key:"Kazakhstani"),
        PickerItem(title:"Kenyan", key:"Kenyan"),
        PickerItem(title:"Kittian and Nevisian", key:"Kittian and Nevisian"),
        PickerItem(title:"Kuwaiti", key:"Kuwaiti"),
        PickerItem(title:"Kyrgyz", key:"Kyrgyz"),
        PickerItem(title:"Laotian", key:"Laotian"),
        PickerItem(title:"Latvian", key:"Latvian"),
        PickerItem(title:"Lebanese", key:"Lebanese"),
        PickerItem(title:"Liberian", key:"Liberian"),
        PickerItem(title:"Libyan", key:"Libyan"),
        PickerItem(title:"Liechtensteiner", key:"Liechtensteiner"),
        PickerItem(title:"Lithuanian", key:"Lithuanian"),
        PickerItem(title:"Luxembourger", key:"Luxembourger"),
        PickerItem(title:"Macedonian", key:"Macedonian"),
        PickerItem(title:"Malagasy", key:"Malagasy"),
        PickerItem(title:"Malawian", key:"Malawian"),
        PickerItem(title:"Malaysian", key:"Malaysian"),
        PickerItem(title:"Maldivan", key:"Maldivan"),
        PickerItem(title:"Malian", key:"Malian"),
        PickerItem(title:"Maltese", key:"Maltese"),
        PickerItem(title:"Marshallese", key:"Marshallese"),
        PickerItem(title:"Mauritanian", key:"Mauritanian"),
        PickerItem(title:"Mauritian", key:"Mauritian"),
        PickerItem(title:"Mexican", key:"Mexican"),
        PickerItem(title:"Micronesian", key:"Micronesian"),
        PickerItem(title:"Moldovan", key:"Moldovan"),
        PickerItem(title:"Monacan", key:"Monacan"),
        PickerItem(title:"Mongolian", key:"Mongolian"),
        PickerItem(title:"Moroccan", key:"Moroccan"),
        PickerItem(title:"Mosotho", key:"Mosotho"),
        PickerItem(title:"Motswana", key:"Motswana"),
        PickerItem(title:"Mozambican", key:"Mozambican"),
        PickerItem(title:"Namibian", key:"Namibian"),
        PickerItem(title:"Nauruan", key:"Nauruan"),
        PickerItem(title:"Nepalese", key:"Nepalese"),
        PickerItem(title:"Netherlander", key:"Netherlander"),
        PickerItem(title:"New Zealander", key:"New Zealander"),
        PickerItem(title:"Ni-Vanuatu", key:"Ni-Vanuatu"),
        PickerItem(title:"Nicaraguan", key:"Nicaraguan"),
        PickerItem(title:"Nigerian", key:"Nigerian"),
        PickerItem(title:"Nigerien", key:"Nigerien"),
        PickerItem(title:"North Korean", key:"North Korean"),
        PickerItem(title:"Northern Irish", key:"Northern Irish"),
        PickerItem(title:"Norwegian", key:"Norwegian"),
        PickerItem(title:"Omani", key:"Omani"),
        PickerItem(title:"Pakistani", key:"Pakistani"),
        PickerItem(title:"Palauan", key:"Palauan"),
        PickerItem(title:"Panamanian", key:"Panamanian"),
        PickerItem(title:"Papua New Guinean", key:"Papua New Guinean"),
        PickerItem(title:"Paraguayan", key:"Paraguayan"),
        PickerItem(title:"Peruvian", key:"Peruvian"),
        PickerItem(title:"Polish", key:"Polish"),
        PickerItem(title:"Portuguese", key:"Portuguese"),
        PickerItem(title:"Qatari", key:"Qatari"),
        PickerItem(title:"Romanian", key:"Romanian"),
        PickerItem(title:"Russian", key:"Russian"),
        PickerItem(title:"Rwandan", key:"Rwandan"),
        PickerItem(title:"Saint Lucian", key:"Saint Lucian"),
        PickerItem(title:"Salvadoran", key:"Salvadoran"),
        PickerItem(title:"Samoan", key:"Samoan"),
        PickerItem(title:"San Marinese", key:"San Marinese"),
        PickerItem(title:"Sao Tomean", key:"Sao Tomean"),
        PickerItem(title:"Saudi", key:"Saudi"),
        PickerItem(title:"Scottish", key:"Scottish"),
        PickerItem(title:"Senegalese", key:"Senegalese"),
        PickerItem(title:"Serbian", key:"Serbian"),
        PickerItem(title:"Seychellois", key:"Seychellois"),
        PickerItem(title:"Sierra Leonean", key:"Sierra Leonean"),
        PickerItem(title:"Singaporean", key:"Singaporean"),
        PickerItem(title:"Slovakian", key:"Slovakian"),
        PickerItem(title:"Slovenian", key:"Slovenian"),
        PickerItem(title:"Solomon Islander", key:"Solomon Islander"),
        PickerItem(title:"Somali", key:"Somali"),
        PickerItem(title:"South African", key:"South African"),
        PickerItem(title:"South Korean", key:"South Korean"),
        PickerItem(title:"Spanish", key:"Spanish"),
        PickerItem(title:"Sri Lankan", key:"Sri Lankan"),
        PickerItem(title:"Sudanese", key:"Sudanese"),
        PickerItem(title:"Surinamer", key:"Surinamer"),
        PickerItem(title:"Swazi", key:"Swazi"),
        PickerItem(title:"Swedish", key:"Swedish"),
        PickerItem(title:"Swiss", key:"Swiss"),
        PickerItem(title:"Syrian", key:"Syrian"),
        PickerItem(title:"Taiwanese", key:"Taiwanese"),
        PickerItem(title:"Tajik", key:"Tajik"),
        PickerItem(title:"Tanzanian", key:"Tanzanian"),
        PickerItem(title:"Thai", key:"Thai"),
        PickerItem(title:"Togolese", key:"Togolese"),
        PickerItem(title:"Tongan", key:"Tongan"),
        PickerItem(title:"Trinidadian or Tobagonian", key:"Trinidadian or Tobagonian"),
        PickerItem(title:"Tunisian", key:"Tunisian"),
        PickerItem(title:"Turkish", key:"Turkish"),
        PickerItem(title:"Tuvaluan", key:"Tuvaluan"),
        PickerItem(title:"Ugandan", key:"Ugandan"),
        PickerItem(title:"Ukrainian", key:"Ukrainian"),
        PickerItem(title:"Uruguayan", key:"Uruguayan"),
        PickerItem(title:"Uzbekistani", key:"Uzbekistani"),
        PickerItem(title:"Venezuelan", key:"Venezuelan"),
        PickerItem(title:"Vietnamese", key:"Vietnamese"),
        PickerItem(title:"Welsh", key:"Welsh"),
        PickerItem(title:"Welsh", key:"Welsh"),
        PickerItem(title:"Yemenite", key:"Yemenite"),
        PickerItem(title:"Zambian", key:"Zambian"),
        PickerItem(title:"Zimbabwean", key:"Zimbabwean"),
    ]
    
    
    var langauges:[PickerItem]  = [
        PickerItem(title: "English", key:"en-us", color:""),
        PickerItem(title: "Spanish", key:"es", color:""),
        PickerItem(title: "Amharic", key:"am", color:""),
        PickerItem(title: "Arabic", key:"ar", color:""),
        PickerItem(title: "Basque", key:"eu", color:""),
        PickerItem(title: "Bengali", key:"bn", color:""),
        PickerItem(title: "Portuguese", key:"BR", color:""),
        PickerItem(title: "Bulgarian", key:"bg", color:""),
        PickerItem(title: "Chinese", key:"CN", color:""),
        PickerItem(title: "Catalan", key:"ca", color:""),
        PickerItem(title: "Cherokee", key:"chr", color:""),
        PickerItem(title: "Croatian", key:"hr", color:""),
        PickerItem(title: "Czech", key:"cs", color:""),
        PickerItem(title: "Danish", key:"da", color:""),
        PickerItem(title: "Dutch", key:"nl", color:""),
        PickerItem(title: "English (United Kingdom)", key:"en-uk", color:""),
        PickerItem(title: "Estonian", key:"et", color:""),
        PickerItem(title: "Tagalog", key:"fil", color:""),
        PickerItem(title: "Visaya", key:"ceb", color:""),
        PickerItem(title: "Ilocano", key:"ilo", color:""),
        PickerItem(title: "Finnish", key:"fi", color:""),
        PickerItem(title: "French", key:"fr", color:""),
        PickerItem(title: "German", key:"de", color:""),
        PickerItem(title: "Greek", key:"el", color:""),
        PickerItem(title: "Gujarati", key:"gu", color:""),
        PickerItem(title: "Hebrew", key:"iw", color:""),
        PickerItem(title: "Hindi", key:"hi", color:""),
        PickerItem(title: "Hungarian", key:"hu", color:""),
        PickerItem(title: "Icelandic", key:"is", color:""),
        PickerItem(title: "Indonesian", key:"id", color:""),
        PickerItem(title: "Italian", key:"it", color:""),
        PickerItem(title: "Japanese", key:"ja", color:""),
        PickerItem(title: "Kannada", key:"kn", color:""),
        PickerItem(title: "Korean", key:"ko", color:""),
        PickerItem(title: "Latvian", key:"lv", color:""),
        PickerItem(title: "Lithuanian", key:"lt", color:""),
        PickerItem(title: "Malay", key:"ms", color:""),
        PickerItem(title: "Malayalam", key:"ml", color:""),
        PickerItem(title: "Marathi", key:"mr", color:""),
        PickerItem(title: "Norwegian", key:"no", color:""),
        PickerItem(title: "Polish", key:"pl", color:""),
        PickerItem(title: "Portuguese", key:"PT", color:""),
        PickerItem(title: "Romanian", key:"ro", color:""),
        PickerItem(title: "Russian", key:"ru", color:""),
        PickerItem(title: "Serbian", key:"sr", color:""),
        PickerItem(title: "Slovak", key:"sk", color:""),
        PickerItem(title: "Slovenian", key:"sl", color:""),
        PickerItem(title: "Swahili", key:"sw", color:""),
        PickerItem(title: "Swedish", key:"sv", color:""),
        PickerItem(title: "Tamil", key:"ta", color:""),
        PickerItem(title: "Telugu", key:"te", color:""),
        PickerItem(title: "Thai", key:"th", color:""),
        PickerItem(title: "Chinese", key:"TW", color:""),
        PickerItem(title: "Turkish", key:"tr", color:""),
        PickerItem(title: "Urdu", key:"ur", color:""),
        PickerItem(title: "Ukrainian", key:"uk", color:""),
        PickerItem(title: "Vietnamese", key:"vi", color:""),
        PickerItem(title: "Welsh", key:"cy", color:""),
        
        
    ]
    
    var scores:[String:[ScoreItem]] = [
        "full_hrp": [
            ScoreItem(question_score: 0, overall_score_range: [0, 60], title: "Urgent Need", color: "#DE6F09"),
            ScoreItem(question_score: 1, overall_score_range: [60, 80], title: "Maintenance", color: "#1470C4"),
            ScoreItem(question_score: 2, overall_score_range: [80, 100], title: "Resilient", color: "#0ABF89"),
        ]
    ]
    
    var states:[PickerItem] = [
        PickerItem(title:"Alaska", key:"Alaska"),
        PickerItem(title:"Alabama", key:"Alabama"),
        PickerItem(title:"Arkansas", key:"Arkansas"),
        PickerItem(title:"American Samoa", key:"American Samoa"),
        PickerItem(title:"Arizona", key:"Arizona"),
        PickerItem(title:"California", key:"California"),
        PickerItem(title:"Colorado", key:"Colorado"),
        PickerItem(title:"Connecticut", key:"Connecticut"),
        PickerItem(title:"District of Columbia", key:"District of Columbia"),
        PickerItem(title:"Delaware", key:"Delaware"),
        PickerItem(title:"Florida", key:"Florida"),
        PickerItem(title:"Georgia", key:"Georgia"),
        PickerItem(title:"Guam", key:"Guam"),
        PickerItem(title:"Hawaii", key:"Hawaii"),
        PickerItem(title:"Iowa", key:"Iowa"),
        PickerItem(title:"Idaho", key:"Idaho"),
        PickerItem(title:"Illinois", key:"Illinois"),
        PickerItem(title:"Indiana", key:"Indiana"),
        PickerItem(title:"Kansas", key:"Kansas"),
        PickerItem(title:"Kentucky", key:"Kentucky"),
        PickerItem(title:"Louisiana", key:"Louisiana"),
        PickerItem(title:"Massachusetts", key:"Massachusetts"),
        PickerItem(title:"Maryland", key:"Maryland"),
        PickerItem(title:"Maine", key:"Maine"),
        PickerItem(title:"Michigan", key:"Michigan"),
        PickerItem(title:"Minnesota", key:"Minnesota"),
        PickerItem(title:"Missouri", key:"Missouri"),
        PickerItem(title:"Mississippi", key:"Mississippi"),
        PickerItem(title:"Montana", key:"Montana"),
        PickerItem(title:"North Carolina", key:"North Carolina"),
        PickerItem(title:" North Dakota", key:" North Dakota"),
        PickerItem(title:"Nebraska", key:"Nebraska"),
        PickerItem(title:"New Hampshire", key:"New Hampshire"),
        PickerItem(title:"New Jersey", key:"New Jersey"),
        PickerItem(title:"New Mexico", key:"New Mexico"),
        PickerItem(title:"Nevada", key:"Nevada"),
        PickerItem(title:"New York", key:"New York"),
        PickerItem(title:"Ohio", key:"Ohio"),
        PickerItem(title:"Oklahoma", key:"Oklahoma"),
        PickerItem(title:"Oregon", key:"Oregon"),
        PickerItem(title:"Pennsylvania", key:"Pennsylvania"),
        PickerItem(title:"Puerto Rico", key:"Puerto Rico"),
        PickerItem(title:"Rhode Island", key:"Rhode Island"),
        PickerItem(title:"South Carolina", key:"South Carolina"),
        PickerItem(title:"South Dakota", key:"South Dakota"),
        PickerItem(title:"Tennessee", key:"Tennessee"),
        PickerItem(title:"Texas", key:"Texas"),
        PickerItem(title:"Utah", key:"Utah"),
        PickerItem(title:"Virginia", key:"Virginia"),
        PickerItem(title:"Virgin Islands", key:"Virgin Islands"),
        PickerItem(title:"Vermont", key:"Vermont"),
        PickerItem(title:"Washington", key:"Washington"),
        PickerItem(title:"Wisconsin", key:"Wisconsin"),
        PickerItem(title:"West Virginia", key:"West Virginia"),
        PickerItem(title:"Wyoming", key:"Wyoming")
    ]
    
    var ethnicities:[PickerItem] = [
        PickerItem(title:"English", key:"English"),
        PickerItem(title:"Welsh", key:"Welsh"),
        PickerItem(title:"Scottish", key:"Scottish"),
        PickerItem(title:"Northern Irish", key:"Northern Irish"),
        PickerItem(title:"Irish", key:"Irish"),
        PickerItem(title:"Gypsy or Irish Traveller", key:"Gypsy or Irish Traveller"),
        PickerItem(title:"White", key:"white"),
        PickerItem(title:"Asian", key:"asian"),
        PickerItem(title:"Filipino", key:"filipino"),
        PickerItem(title:"White and Black Caribbean", key:"White and Black Caribbean"),
        PickerItem(title:"White and Black African", key:"White and Black African"),
        PickerItem(title:"Indian", key:"Indian"),
        PickerItem(title:"Pakistani", key:"Pakistani"),
        PickerItem(title:"Bangladeshi", key:"Bangladeshi"),
        PickerItem(title:"Chinese", key:"Chinese"),
        PickerItem(title:"African", key:"African"),
        PickerItem(title:"African American", key:"African American"),
        PickerItem(title:"Caribbean", key:"Caribbean"),
        PickerItem(title:"Arab", key:"Arab"),
        PickerItem(title:"Hispanic", key:"Hispanic"),
        PickerItem(title:"Latino", key:"Latino"),
        PickerItem(title:"Hispanic / Latino", key:"Hispanic / Latino"),
        PickerItem(title:"Native American", key:"Native American"),
        PickerItem(title:"Pacific Islander", key:"Pacific Islander"),
        PickerItem(title:"Race/ethnicity unknown", key:"unknown"),
        PickerItem(title:"Does not wish to provide.", key:"declined")
    ]
    
    var timeZones:[PickerItem] = [
        PickerItem(title:"Midway Island, Samoa", key:"Pacific/Midway"),
        PickerItem(title:"Pago Pago", key:"Pacific/Pago_Pago"),
        PickerItem(title:"Hawaii", key:"Pacific/Honolulu"),
        PickerItem(title:"Alaska", key:"America/Anchorage"),
        PickerItem(title:"Vancouver", key:"America/Vancouver"      ),
        PickerItem(title:"Pacific Time (US and Canada)", key:"America/Los_Angeles"    ),
        PickerItem(title:"Tijuana", key:"America/Tijuana"        ),
        PickerItem(title:"Edmonton", key:"America/Edmonton"       ),
        PickerItem(title:"Mountain Time (US and Canada)", key:"America/Denver"         ),
        PickerItem(title:"Arizona", key:"America/Phoenix"        ),
        PickerItem(title:"Mazatlan", key:"America/Mazatlan"       ),
        PickerItem(title:"Winnipeg", key:"America/Winnipeg"       ),
        PickerItem(title:"Saskatchewan", key:"America/Regina"         ),
        PickerItem(title:"Central Time (US and Canada)", key:"America/Chicago"        ),
        PickerItem(title:"Mexico City", key:"America/Mexico_City"    ),
        PickerItem(title:"Guatemala", key:"America/Guatemala"      ),
        PickerItem(title:"El Salvador", key:"America/El_Salvador"                  ),
        PickerItem(title:"Managua", key:"America/Managua"                      ),
        PickerItem(title:"Costa Rica", key:"America/Costa_Rica"                   ),
        PickerItem(title:"Montreal", key:"America/Montreal"                     ),
        PickerItem(title:"Eastern Time (US and Canada)", key:"America/New_York"                     ),
        PickerItem(title:"Indiana (East)", key:"America/Indianapolis"                 ),
        PickerItem(title:"Panama", key:"America/Panama"                       ),
        PickerItem(title:"Bogota", key:"America/Bogota"                       ),
        PickerItem(title:"Lima", key:"America/Lima"                         ),
        PickerItem(title:"Halifax", key:"America/Halifax"                      ),
        PickerItem(title:"Puerto Rico", key:"America/Puerto_Rico"                  ),
        PickerItem(title:"Caracas", key:"America/Caracas"                      ),
        PickerItem(title:"Santiago", key:"America/Santiago"                     ),
        PickerItem(title:"Newfoundland and Labrador", key:"America/St_Johns"                     ),
        PickerItem(title:"Montevideo", key:"America/Montevideo"                   ),
        PickerItem(title:"Brasilia", key:"America/Araguaina"                    ),
        PickerItem(title:"Buenos Aires, Georgetown", key:"America/Argentina/Buenos_Aires"       ),
        PickerItem(title:"Greenland", key:"America/Godthab"                      ),
        PickerItem(title:"Sao Paulo", key:"America/Sao_Paulo"                    ),
        PickerItem(title:"Azores", key:"Atlantic/Azores"                      ),
        PickerItem(title:"Atlantic Time (Canada)", key:"Canada/Atlantic"                      ),
        PickerItem(title:"Cape Verde Islands", key:"Atlantic/Cape_Verde"                  ),
        PickerItem(title:"Universal Time UTC", key:"UTC"                                  ),
        PickerItem(title:"Greenwich Mean Time", key:"Etc/Greenwich"                        ),
        PickerItem(title:"Belgrade, Bratislava,Ljubljana", key:"Europe/Belgrade"                      ),
        PickerItem(title:"Sarajevo,Skopje,Zagreb", key:"CET"                                  ),
        PickerItem(title:"Reykjavik", key:"Atlantic/Reykjavik"                   ),
        PickerItem(title:"Dublin", key:"Europe/Dublin"                        ),
        PickerItem(title:"London", key:"Europe/London"                        ),
        PickerItem(title:"Lisbon", key:"Europe/Lisbon"                        ),
        PickerItem(title:"Casablanca", key:"Africa/Casablanca"                    ),
        PickerItem(title:"Nouakchott", key:"Africa/Nouakchott"                    ),
        PickerItem(title:"Oslo", key:"Europe/Oslo"                          ),
        PickerItem(title:"Copenhagen", key:"Europe/Copenhagen"                    ),
        PickerItem(title:"Brussels", key:"Europe/Brussels"                      ),
        PickerItem(title:"Amsterdam,Berlin,Rome,Stockholm, Vienna", key:"Europe/Berlin"                        ),
        PickerItem(title:"Helsinki", key:"Europe/Helsinki"                      ),
        PickerItem(title:"Amsterdam", key:"Europe/Amsterdam"       ),
        PickerItem(title:"Rome", key:"Europe/Rome"            ),
        PickerItem(title:"Stockholm", key:"Europe/Stockholm"       ),
        PickerItem(title:"Vienna", key:"Europe/Vienna"          ),
        PickerItem(title:"Luxembourg", key:"Europe/Luxembourg"      ),
        PickerItem(title:"Paris", key:"Europe/Paris"           ),
        PickerItem(title:"Zurich", key:"Europe/Zurich"          ),
        PickerItem(title:"Madrid", key:"Europe/Madrid"          ),
        PickerItem(title:"West Central Africa", key:"Africa/Bangui"          ),
        PickerItem(title:"Algiers", key:"Africa/Algiers"         ),
        PickerItem(title:"Tunis", key:"Africa/Tunis"           ),
        PickerItem(title:"Harare, Pretoria", key:"Africa/Harare"          ),
        PickerItem(title:"Nairobi", key:"Africa/Nairobi"         ),
        PickerItem(title:"Warsaw", key:"Europe/Warsaw"          ),
        PickerItem(title:"Prague Bratislava", key:"Europe/Prague"          ),
        PickerItem(title:"Budapest", key:"Europe/Budapest"        ),
        PickerItem(title:"Sofia", key:"Europe/Sofia"           ),
        PickerItem(title:"Istanbul", key:"Europe/Istanbul"        ),
        PickerItem(title:"Athens", key:"Europe/Athens"          ),
        PickerItem(title:"Bucharest", key:"Europe/Bucharest"       ),
        PickerItem(title:"Nicosia", key:"Asia/Nicosia"           ),
        PickerItem(title:"Beirut", key:"Asia/Beirut"            ),
        PickerItem(title:"Damascus", key:"Asia/Damascus"          ),
        PickerItem(title:"Jerusalem", key:"Asia/Jerusalem"         ),
        PickerItem(title:"Amman", key:"Asia/Amman"             ),
        PickerItem(title:"Tripoli", key:"Africa/Tripoli"         ),
        PickerItem(title:"Cairo", key:"Africa/Cairo"           ),
        PickerItem(title:"Johannesburg", key:"Africa/Johannesburg"    ),
        PickerItem(title:"Moscow", key:"Europe/Moscow"          ),
        PickerItem(title:"Baghdad", key:"Asia/Baghdad"           ),
        PickerItem(title:"Kuwait", key:"Asia/Kuwait"            ),
        
        PickerItem(title:"Riyadh", key:"Asia/Riyadh"),
        PickerItem(title:"Bahrain", key:"Asia/Bahrain"),
        
        PickerItem(title:"Qatar",key:"Asia/Qatar"                           ),
        PickerItem(title:"Aden",key:"Asia/Aden"                            ),
        PickerItem(title:"Tehran",key:"Asia/Tehran"                          ),
        PickerItem(title:"Khartoum",key:"Africa/Khartoum"                      ),
        PickerItem(title:"Djibouti",key:"Africa/Djibouti"                      ),
        PickerItem(title:"Mogadishu",key:"Africa/Mogadishu"                     ),
        PickerItem(title:"Dubai",key:"Asia/Dubai"                           ),
        PickerItem(title:"Muscat",key:"Asia/Muscat"                          ),
        PickerItem(title:"Baku, Tbilisi, Yerevan",key:"Asia/Baku"                            ),
        PickerItem(title:"Kabul",key:"Asia/Kabul"                           ),
        PickerItem(title:"Yekaterinburg",key:"Asia/Yekaterinburg"                   ),
        PickerItem(title:"Islamabad, Karachi, Tashkent",key:"Asia/Tashkent"                        ),
        PickerItem(title:"India",key:"Asia/Calcutta"                        ),
        PickerItem(title:"Kathmandu",key:"Asia/Kathmandu"                       ),
        PickerItem(title:"Novosibirsk",key:"Asia/Novosibirsk"                     ),
        PickerItem(title:"Almaty",key:"Asia/Almaty"                          ),
        PickerItem(title:"Dacca",key:"Asia/Dacca"                           ),
        PickerItem(title:"Krasnoyarsk",key:"Asia/Krasnoyarsk"                     ),
        PickerItem(title:"Astana, Dhaka",key:"Asia/Dhaka"                           ),
        PickerItem(title:"Bangkok",key:"Asia/Bangkok"                         ),
        PickerItem(title:"Vietnam",key:"Asia/Saigon"                          ),
        PickerItem(title:"Jakarta",key:"Asia/Jakarta"                         ),
        PickerItem(title:"Irkutsk, Ulaanbaatar",key:"Asia/Irkutsk"                         ),
        PickerItem(title:"Beijing, Shanghai",key:"Asia/Shanghai"                        ),
        PickerItem(title:"Hong Kong",key:"Asia/Hong_Kong"                       ),
        PickerItem(title:"Taipei",key:"Asia/Taipei"                          ),
        PickerItem(title:"Kuala Lumpur",key:"Asia/Kuala_Lumpur"                    ),
        PickerItem(title:"Singapore",key:"Asia/Singapore"                       ),
        PickerItem(title:"Perth",key:"Australia/Perth"                      ),
        PickerItem(title:"Yakutsk",key:"Asia/Yakutsk"                         ),
        PickerItem(title:"Seoul",key:"Asia/Seoul"                           ),
        PickerItem(title:"Osaka, Sapporo, Tokyo",key:"Asia/Tokyo"                           ),
        PickerItem(title:"Darwin", key:"Australia/Darwin"                     ),
        PickerItem(title:"Adelaide", key:"Australia/Adelaide"                   ),
        PickerItem(title:"Vladivostok", key:"Asia/Vladivostok"                     ),
        PickerItem(title:"Guam, Port Moresby", key:"Pacific/Port_Moresby"                 ),
        PickerItem(title:"Brisbane", key:"Australia/Brisbane"                   ),
        PickerItem(title:"Canberra, Melbourne,Sydney", key:"Australia/Sydney"                     ),
        PickerItem(title:"Hobart", key:"Australia/Hobart"                     ),
        PickerItem(title:"Magadan", key:"Asia/Magadan"                         ),
        PickerItem(title:"Islands", key:"SST    Solomon"                       ),
        PickerItem(title:"New Caledonia", key:"Pacific/Noumea"                       ),
        PickerItem(title:"Kamchatka", key:"Asia/Kamchatka"                       ),
        PickerItem(title:"Fiji Islands, Marshall Islands", key:"Pacific/Fiji"                         ),
        PickerItem(title:"Auckland, Wellington", key:"Pacific/Auckland"                     ),
        PickerItem(title:"Mumbai, Kolkata, New Delhi", key:"Asia/Kolkata"                         ),
        PickerItem(title:"Kiev", key:"Europe/Kiev"                          ),
        PickerItem(title:"Tegucigalpa", key:"America/Tegucigalpa"                  ),
        PickerItem(title:"Independent State of Samoa", key:"Pacific/Apia"                         )]
    
    
    var breeds = [PickerItem(title:"affenpinscher", key: "affenpinscher"),
                  PickerItem(title:"Afghan hound", key: "Afghan hound"),
                  PickerItem(title:"Airedale terrier", key: "Airedale terrier"),
                  PickerItem(title:"Akita", key: "Akita"),
                  PickerItem(title:"Alaskan Malamute", key: "Alaskan Malamute"),
                  PickerItem(title:"American Staffordshire terrier", key: "American Staffordshire terrier"),
                  PickerItem(title:"American water spaniel", key: "American water spaniel"),
                  PickerItem(title:"Australian cattle dog", key: "Australian cattle dog"),
                  PickerItem(title:"Australian shepherd", key: "Australian shepherd"),
                  PickerItem(title:"Australian terrier", key: "Australian terrier"),
                  PickerItem(title:"basenji", key: "basenji"),
                  PickerItem(title:"basset hound", key: "basset hound"),
                  PickerItem(title:"beagle", key: "beagle"),
                  PickerItem(title:"bearded collie", key: "bearded collie"),
                  PickerItem(title:"Bedlington terrier", key: "Bedlington terrier"),
                  PickerItem(title:"Bernese mountain dog", key: "Bernese mountain dog"),
                  PickerItem(title:"bichon frise", key: "bichon frise"),
                  PickerItem(title:"black and tan coonhound", key: "black and tan coonhound"),
                  PickerItem(title:"bloodhound", key: "bloodhound"),
                  PickerItem(title:"border collie", key: "border collie"),
                  PickerItem(title:"border terrier", key: "border terrier"),
                  PickerItem(title:"borzoi", key: "borzoi"),
                  PickerItem(title:"Boston terrier", key: "Boston terrier"),
                  PickerItem(title:"bouvier des Flandres", key: "bouvier des Flandres"),
                  PickerItem(title:"boxer", key: "boxer"),
                  PickerItem(title:"briard", key: "briard"),
                  PickerItem(title:"Brittany", key: "Brittany"),
                  PickerItem(title:"Brussels griffon", key: "Brussels griffon"),
                  PickerItem(title:"bull terrier", key: "bull terrier"),
                  PickerItem(title:"bulldog", key: "bulldog"),
                  PickerItem(title:"bullmastiff", key: "bullmastiff"),
                  PickerItem(title:"cairn terrier", key: "cairn terrier"),
                  PickerItem(title:"Canaan dog", key: "Canaan dog"),
                  PickerItem(title:"Chesapeake Bay retriever", key: "Chesapeake Bay retriever"),
                  PickerItem(title:"Chihuahua", key: "Chihuahua"),
                  PickerItem(title:"Chinese crested", key: "Chinese crested"),
                  PickerItem(title:"Chinese shar-pei", key: "Chinese shar-pei"),
                  PickerItem(title:"chow chow", key: "chow chow"),
                  PickerItem(title:"Clumber spaniel", key: "Clumber spaniel"),
                  PickerItem(title:"cocker spaniel", key: "cocker spaniel"),
                  PickerItem(title:"collie", key: "collie"),
                  PickerItem(title:"curly-coated retriever", key: "curly-coated retriever"),
                  PickerItem(title:"dachshund", key: "dachshund"),
                  PickerItem(title:"Dalmatian", key: "Dalmatian"),
                  PickerItem(title:"Doberman pinscher", key: "Doberman pinscher"),
                  PickerItem(title:"English cocker spaniel", key: "English cocker spaniel"),
                  PickerItem(title:"English setter", key: "English setter"),
                  PickerItem(title:"English springer spaniel", key: "English springer spaniel"),
                  PickerItem(title:"English toy spaniel", key: "English toy spaniel"),
                  PickerItem(title:"Eskimo dog", key: "Eskimo dog"),
                  PickerItem(title:"Finnish spitz", key: "Finnish spitz"),
                  PickerItem(title:"flat-coated retriever", key: "flat-coated retriever"),
                  PickerItem(title:"fox terrier", key: "fox terrier"),
                  PickerItem(title:"foxhound", key: "foxhound"),
                  PickerItem(title:"French bulldog", key: "French bulldog"),
                  PickerItem(title:"German shepherd", key: "German shepherd"),
                  PickerItem(title:"German shorthaired pointer", key: "German shorthaired pointer"),
                  PickerItem(title:"German wirehaired pointer", key: "German wirehaired pointer"),
                  PickerItem(title:"golden retriever", key: "golden retriever"),
                  PickerItem(title:"Gordon setter", key: "Gordon setter"),
                  PickerItem(title:"Great Dane", key: "Great Dane"),
                  PickerItem(title:"greyhound", key: "greyhound"),
                  PickerItem(title:"Irish setter", key: "Irish setter"),
                  PickerItem(title:"Irish water spaniel", key: "Irish water spaniel"),
                  PickerItem(title:"Irish wolfhound", key: "Irish wolfhound"),
                  PickerItem(title:"Jack Russell terrier", key: "Jack Russell terrier"),
                  PickerItem(title:"Japanese spaniel", key: "Japanese spaniel"),
                  PickerItem(title:"keeshond", key: "keeshond"),
                  PickerItem(title:"Kerry blue terrier", key: "Kerry blue terrier"),
                  PickerItem(title:"komondor", key: "komondor"),
                  PickerItem(title:"kuvasz", key: "kuvasz"),
                  PickerItem(title:"Labrador retriever", key: "Labrador retriever"),
                  PickerItem(title:"Lakeland terrier", key: "Lakeland terrier"),
                  PickerItem(title:"Lhasa apso", key: "Lhasa apso"),
                  PickerItem(title:"Maltese", key: "Maltese"),
                  PickerItem(title:"Manchester terrier", key: "Manchester terrier"),
                  PickerItem(title:"mastiff", key: "mastiff"),
                  PickerItem(title:"Mexican hairless", key: "Mexican hairless"),
                  PickerItem(title:"Newfoundland", key: "Newfoundland"),
                  PickerItem(title:"Norwegian elkhound", key: "Norwegian elkhound"),
                  PickerItem(title:"Norwich terrier", key: "Norwich terrier"),
                  PickerItem(title:"otterhound", key: "otterhound"),
                  PickerItem(title:"papillon", key: "papillon"),
                  PickerItem(title:"Pekingese", key: "Pekingese"),
                  PickerItem(title:"pointer", key: "pointer"),
                  PickerItem(title:"Pomeranian", key: "Pomeranian"),
                  PickerItem(title:"poodle", key: "poodle"),
                  PickerItem(title:"pug", key: "pug"),
                  PickerItem(title:"puli", key: "puli"),
                  PickerItem(title:"Rhodesian ridgeback", key: "Rhodesian ridgeback"),
                  PickerItem(title:"Rottweiler", key: "Rottweiler"),
                  PickerItem(title:"Saint Bernard", key: "Saint Bernard"),
                  PickerItem(title:"saluki", key: "saluki"),
                  PickerItem(title:"Samoyed", key: "Samoyed"),
                  PickerItem(title:"schipperke", key: "schipperke"),
                  PickerItem(title:"schnauzer", key: "schnauzer"),
                  PickerItem(title:"Scottish deerhound", key: "Scottish deerhound"),
                  PickerItem(title:"Scottish terrier", key: "Scottish terrier"),
                  PickerItem(title:"Sealyham terrier", key: "Sealyham terrier"),
                  PickerItem(title:"Shetland sheepdog", key: "Shetland sheepdog"),
                  PickerItem(title:"shih tzu", key: "shih tzu"),
                  PickerItem(title:"Siberian husky", key: "Siberian husky"),
                  PickerItem(title:"silky terrier", key: "silky terrier"),
                  PickerItem(title:"Skye terrier", key: "Skye terrier"),
                  PickerItem(title:"Staffordshire bull terrier", key: "Staffordshire bull terrier"),
                  PickerItem(title:"soft-coated wheaten terrier", key: "soft-coated wheaten terrier"),
                  PickerItem(title:"Sussex spaniel", key: "Sussex spaniel"),
                  PickerItem(title:"spitz", key: "spitz"),
                  PickerItem(title:"Tibetan terrier", key: "Tibetan terrier"),
                  PickerItem(title:"vizsla", key: "vizsla"),
                  PickerItem(title:"Weimaraner", key: "Weimaraner"),
                  PickerItem(title:"Welsh terrier", key: "Welsh terrier"),
                  PickerItem(title:"West Highland white terrier", key: "West Highland white terrier"),
                  PickerItem(title:"whippet", key: "whippet"),
                  PickerItem(title:"Yorkshire terrier", key: "Yorkshire terrier")]
    
}
