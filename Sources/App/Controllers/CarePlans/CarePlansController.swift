//
//  CarePlansController.swift
//  hmbl-core
//
//  Created by <PERSON> on 5/28/25.
//
import Foundation
import Vapor
import Fluent
import JWTKit
import APNS
// routes.swift
struct CarePlansController: RouteCollection {
   
    
    // routes.swift
    func boot(routes: any Vapor.RoutesBuilder) throws {
        let carePlans = routes.grouped("api", "members", ":memberID", "careplans")
        carePlans.post(use: createCarePlan)
        carePlans.get(use: listCarePlans)

        let carePlan = routes.grouped("api", "careplans", ":carePlanID")
        carePlan.get(use: getCarePlan)
        carePlan.put(use: updateCarePlan)
        carePlan.delete(use: deleteCarePlan)

        // Goals
        let goals = carePlan.grouped("goals")
        goals.post(use: createGoal)
        goals.get(use: listGoals)
        goals.put(":goalID", use: updateGoal)
        goals.delete(":goalID", use: deleteGoal)

        // Interventions
        let interventions = carePlan.grouped("interventions")
        interventions.post(use: createIntervention)
        interventions.get(use: listInterventions)
        interventions.put(":interventionID", use: updateIntervention)
        interventions.delete(":interventionID", use: deleteIntervention)

        // Problems
        let problems = carePlan.grouped("problems")
        problems.post(use: createProblem)
        problems.get(use: listProblems)
        problems.put(":problemID", use: updateProblem)
        problems.delete(":problemID", use: deleteProblem)

        // Care Team Members
        let teamMembers = carePlan.grouped("team-members")
        teamMembers.post(use: createCareTeamMember)
        teamMembers.get(use: listCareTeamMembers)
        teamMembers.put(":memberID", use: updateCareTeamMember)
        teamMembers.delete(":memberID", use: deleteCareTeamMember)

        // Reviews
        let reviews = carePlan.grouped("reviews")
        reviews.post(use: createCarePlanReview)
        reviews.get(use: listCarePlanReviews)
        reviews.put(":reviewID", use: updateCarePlanReview)
        reviews.delete(":reviewID", use: deleteCarePlanReview)

        // Follow-ups
        let followUps = carePlan.grouped("followups")
        followUps.post(use: createFollowUp)
        followUps.get(use: listFollowUps)
        followUps.get(":followUpID", use: getFollowUp)
        followUps.put(":followUpID", use: updateFollowUp)
        followUps.delete(":followUpID", use: deleteFollowUp)

        // Services
        let services = carePlan.grouped("services")
        services.post(use: createCarePlanService)
        services.get(use: listCarePlanServices)
        services.get(":serviceID", use: getCarePlanService)
        services.put(":serviceID", use: updateCarePlanService)
        services.delete(":serviceID", use: deleteCarePlanService)

        // Timeline Items
        let timelineItems = carePlan.grouped("timeline-items")
        timelineItems.post(use: createTimelineItem)
        timelineItems.get(use: listTimelineItems)
        timelineItems.get(":timelineItemID", use: getTimelineItem)
        timelineItems.put(":timelineItemID", use: updateTimelineItem)
        timelineItems.delete(":timelineItemID", use: deleteTimelineItem)
    }
    
    
    // Controller stubs for CarePlanService
    func createCarePlanService(req: Request) async throws -> CarePlanService {
        var service = try req.content.decode(CarePlanService.self)
        service.$carePlan.id = try req.parameters.require("carePlanID", as: UUID.self)
        try await service.save(on: req.db)
        return service
    }

    func listCarePlanServices(req: Request) async throws -> [CarePlanService] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await CarePlanService.query(on: req.db)
            .filter(\.$carePlan.$id == carePlanID).all()
    }

    func getCarePlanService(req: Request) async throws -> CarePlanService {
        let id = try req.parameters.require("serviceID", as: UUID.self)
        guard let service = try await CarePlanService.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        return service
    }

    func updateCarePlanService(req: Request) async throws -> CarePlanService {
        let id = try req.parameters.require("serviceID", as: UUID.self)
        let input = try req.content.decode(CarePlanService.self)
        guard let service = try await CarePlanService.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        service.cboName = input.cboName
        service.staffName = input.staffName
        service.addedBy = input.addedBy
        service.status = input.status
        service.appointmentDate = input.appointmentDate
        service.outcomeReasonType = input.outcomeReasonType
        service.outcomeReasonDescription = input.outcomeReasonDescription
        try await service.update(on: req.db)
        return service
    }

    func deleteCarePlanService(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("serviceID", as: UUID.self)
        guard let service = try await CarePlanService.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await service.delete(on: req.db)
        return .noContent
    }
    
    // Controller stubs for CarePlanFollowUp
    func createFollowUp(req: Request) async throws -> CarePlanFollowUp {
        var followUp = try req.content.decode(CarePlanFollowUp.self)
        followUp.$carePlan.id = try req.parameters.require("carePlanID", as: UUID.self)
        try await followUp.save(on: req.db)
        return followUp
    }

    func listFollowUps(req: Request) async throws -> [CarePlanFollowUp] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await CarePlanFollowUp.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }

    func getFollowUp(req: Request) async throws -> CarePlanFollowUp {
        let followUpID = try req.parameters.require("followUpID", as: UUID.self)
        guard let followUp = try await CarePlanFollowUp.find(followUpID, on: req.db) else {
            throw Abort(.notFound)
        }
        return followUp
    }

    func updateFollowUp(req: Request) async throws -> CarePlanFollowUp {
        let followUpID = try req.parameters.require("followUpID", as: UUID.self)
        let input = try req.content.decode(CarePlanFollowUp.self)
        guard let followUp = try await CarePlanFollowUp.find(followUpID, on: req.db) else {
            throw Abort(.notFound)
        }
        followUp.datetime = input.datetime
        followUp.type = input.type
        followUp.outcome = input.outcome
        followUp.notes = input.notes
        followUp.staffName = input.staffName
        followUp.staffRole = input.staffRole
        try await followUp.update(on: req.db)
        return followUp
    }

    func deleteFollowUp(req: Request) async throws -> HTTPStatus {
        let followUpID = try req.parameters.require("followUpID", as: UUID.self)
        guard let followUp = try await CarePlanFollowUp.find(followUpID, on: req.db) else {
            throw Abort(.notFound)
        }
        try await followUp.delete(on: req.db)
        return .noContent
    }
    
    // Controller stubs for CarePlanReview
    func createCarePlanReview(req: Request) async throws -> CarePlanReview {
        var review = try req.content.decode(CarePlanReview.self)
        review.$carePlan.id = try req.parameters.require("carePlanID", as: UUID.self)
        try await review.save(on: req.db)
        return review
    }

    func listCarePlanReviews(req: Request) async throws -> [CarePlanReview] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await CarePlanReview.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }
    
    // Controller stubs for CareTeamMember
    func createCareTeamMember(req: Request) async throws -> CareTeamMember {
        var member = try req.content.decode(CareTeamMember.self)
        member.$carePlan.id = try req.parameters.require("carePlanID", as: UUID.self)
        try await member.save(on: req.db)
        return member
    }

    func listCareTeamMembers(req: Request) async throws -> [CareTeamMember] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await CareTeamMember.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }
    
    // Controller stubs for Problem
    func createProblem(req: Request) async throws -> Problem {
        var problem = try req.content.decode(Problem.self)
        problem.$carePlan.id = try req.parameters.require("carePlanID", as: UUID.self)
        try await problem.save(on: req.db)
        return problem
    }

    func listProblems(req: Request) async throws -> [Problem] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await Problem.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }
    
    // Controller stubs for Intervention
    func createIntervention(req: Request) async throws -> Intervention {
        var intervention = try req.content.decode(Intervention.self)
        intervention.$carePlan.id = try req.parameters.require("carePlanID", as: UUID.self)
        try await intervention.save(on: req.db)
        return intervention
    }

    func listInterventions(req: Request) async throws -> [Intervention] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await Intervention.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }
    
    // Controller stubs for Goal
    func createGoal(req: Request) async throws -> Goal {
        var goal = try req.content.decode(Goal.self)
        goal.$carePlan.id = try req.parameters.require("carePlanID", as: UUID.self)
        try await goal.save(on: req.db)
        return goal
    }

    func listGoals(req: Request) async throws -> [Goal] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await Goal.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }
    
    // Primary Care Plan Controller Stubs
    func createCarePlan(req: Request) async throws -> CarePlan {
        let input = try req.content.decode(CarePlanCreateRequest.self)
        let memberID = try req.parameters.require("memberID", as: UUID.self)

        let carePlan = CarePlan()
        carePlan.$member.id = memberID
        carePlan.startDate = input.startDate
        carePlan.lastReviewed = input.lastReviewed
        carePlan.nextReviewDate = input.nextReviewDate
        carePlan.outcome = input.outcome
        carePlan.status = input.status

        try await carePlan.save(on: req.db)
        return carePlan
    }

    func listCarePlans(req: Request) async throws -> [CarePlan] {
        let memberID = try req.parameters.require("memberID", as: UUID.self)

        var query = CarePlan.query(on: req.db).filter(\.$member.$id == memberID)

        // Add status filtering if provided
        if let status = try? req.query.get(String.self, at: "status") {
            query = query.filter(\.$status == status)
        }

        return try await query.sort(\.$createdAt, .descending).all()
    }

    func getCarePlan(req: Request) async throws -> CarePlan {
        let id = try req.parameters.require("carePlanID", as: UUID.self)
        guard let plan = try await CarePlan.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        return plan
    }

    func updateCarePlan(req: Request) async throws -> CarePlan {
        let id = try req.parameters.require("carePlanID", as: UUID.self)
        let input = try req.content.decode(CarePlanCreateRequest.self)
        guard let plan = try await CarePlan.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        plan.startDate = input.startDate
        plan.lastReviewed = input.lastReviewed
        plan.nextReviewDate = input.nextReviewDate
        plan.outcome = input.outcome
        plan.status = input.status
        try await plan.update(on: req.db)
        return plan
    }

    func deleteCarePlan(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("carePlanID", as: UUID.self)
        guard let plan = try await CarePlan.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await plan.delete(on: req.db)
        return .noContent
    }

    // Controller extensions
    func updateGoal(req: Request) async throws -> Goal {
        let id = try req.parameters.require("goalID", as: UUID.self)
        let input = try req.content.decode(Goal.self)
        guard let goal = try await Goal.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        goal.description = input.description
        goal.targetDate = input.targetDate
        goal.status = input.status
        try await goal.update(on: req.db)
        return goal
    }

    func deleteGoal(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("goalID", as: UUID.self)
        guard let goal = try await Goal.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await goal.delete(on: req.db)
        return .noContent
    }

    func updateIntervention(req: Request) async throws -> Intervention {
        let id = try req.parameters.require("interventionID", as: UUID.self)
        let input = try req.content.decode(Intervention.self)
        guard let intervention = try await Intervention.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        intervention.action = input.action
        intervention.responsibleParty = input.responsibleParty
        intervention.dueDate = input.dueDate
        try await intervention.update(on: req.db)
        return intervention
    }

    func deleteIntervention(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("interventionID", as: UUID.self)
        guard let intervention = try await Intervention.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await intervention.delete(on: req.db)
        return .noContent
    }

    func updateProblem(req: Request) async throws -> Problem {
        let id = try req.parameters.require("problemID", as: UUID.self)
        let input = try req.content.decode(Problem.self)
        guard let problem = try await Problem.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        problem.icdCode = input.icdCode
        problem.description = input.description
        problem.clinicalNote = input.clinicalNote
        problem.status = input.status
        problem.dateIdentified = input.dateIdentified
        problem.source = input.source
        problem.confirmedBy = input.confirmedBy
        try await problem.update(on: req.db)
        return problem
    }

    func deleteProblem(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("problemID", as: UUID.self)
        guard let problem = try await Problem.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await problem.delete(on: req.db)
        return .noContent
    }

    func updateCareTeamMember(req: Request) async throws -> CareTeamMember {
        let id = try req.parameters.require("memberID", as: UUID.self)
        let input = try req.content.decode(CareTeamMember.self)
        guard let member = try await CareTeamMember.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        member.userID = input.userID
        member.name = input.name
        member.role = input.role
        member.contactInfo = input.contactInfo
        try await member.update(on: req.db)
        return member
    }

    func deleteCareTeamMember(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("memberID", as: UUID.self)
        guard let member = try await CareTeamMember.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await member.delete(on: req.db)
        return .noContent
    }

    func updateCarePlanReview(req: Request) async throws -> CarePlanReview {
        let id = try req.parameters.require("reviewID", as: UUID.self)
        let input = try req.content.decode(CarePlanReview.self)
        guard let review = try await CarePlanReview.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        review.reviewDate = input.reviewDate
        review.notes = input.notes
        review.reviewerName = input.reviewerName
        review.reviewerRole = input.reviewerRole
        try await review.update(on: req.db)
        return review
    }

    func deleteCarePlanReview(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("reviewID", as: UUID.self)
        guard let review = try await CarePlanReview.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await review.delete(on: req.db)
        return .noContent
    }

    // MARK: - Timeline Items

    func createTimelineItem(req: Request) async throws -> TimelineItem {
        var timelineItem = try req.content.decode(TimelineItem.self)
        timelineItem.$carePlan.id = try req.parameters.require("carePlanID", as: UUID.self)
        try await timelineItem.save(on: req.db)
        return timelineItem
    }

    func listTimelineItems(req: Request) async throws -> [TimelineItem] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await TimelineItem.query(on: req.db)
            .filter(\.$carePlan.$id == carePlanID)
            .sort(\.$createdAt, .descending)
            .all()
    }

    func getTimelineItem(req: Request) async throws -> TimelineItem {
        let id = try req.parameters.require("timelineItemID", as: UUID.self)
        guard let timelineItem = try await TimelineItem.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        return timelineItem
    }

    func updateTimelineItem(req: Request) async throws -> TimelineItem {
        let id = try req.parameters.require("timelineItemID", as: UUID.self)
        let input = try req.content.decode(TimelineItem.self)
        guard let timelineItem = try await TimelineItem.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        timelineItem.carepackageID = input.carepackageID
        timelineItem.title = input.title
        timelineItem.status = input.status
        timelineItem.desc = input.desc
        timelineItem.visible = input.visible
        timelineItem.memberId = input.memberId
        timelineItem.meta = input.meta

        try await timelineItem.update(on: req.db)
        return timelineItem
    }

    func deleteTimelineItem(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("timelineItemID", as: UUID.self)
        guard let timelineItem = try await TimelineItem.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await timelineItem.delete(on: req.db)
        return .noContent
    }
}


final class CarePlan: Model, @unchecked Sendable {
    static let schema = "care_plans"

    @ID var id: UUID?
    @Parent(key: "member_id") var member: Member

    @Field(key: "start_date") var startDate: Date
    @Field(key: "last_reviewed") var lastReviewed: Date?
    @Field(key: "next_review_date") var nextReviewDate: Date?
    @Field(key: "outcome") var outcome: String?
    @Field(key: "status") var status: String

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?

    @Children(for: \.$carePlan) var goals: [Goal]
    @Children(for: \.$carePlan) var interventions: [Intervention]
    @Children(for: \.$carePlan) var problems: [Problem]
    @Children(for: \.$carePlan) var teamMembers: [CareTeamMember]
    @Children(for: \.$carePlan) var reviews: [CarePlanReview]
    @Children(for: \.$carePlan) var followUps: [CarePlanFollowUp]
    @Children(for: \.$carePlan) var services: [CarePlanService]
    @Children(for: \.$carePlan) var timelineItems: [TimelineItem]
}

// MARK: - CarePlan Content Conformance
extension CarePlan: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case startDate = "start_date"
        case lastReviewed = "last_reviewed"
        case nextReviewDate = "next_review_date"
        case outcome
        case status
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: member is excluded from coding keys to prevent decoding issues
    }
}


struct CreateCarePlan: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plans")
            .id()
            .field("member_id", .uuid, .required, .references("members", "id", onDelete: .cascade))
            .field("start_date", .date, .required)
            .field("last_reviewed", .date)
            .field("next_review_date", .date)
            .field("outcome", .string)
            .field("status", .string, .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plans").delete()
    }
}


final class Goal: Model, @unchecked Sendable {
    static let schema = "goals"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "description") var description: String
     @Field(key: "type") var type: String
     @Field(key: "target_date") var targetDate: Date
     @Field(key: "status") var status: String
     @Field(key: "outcome") var outcome: String?

     @Field(key: "objective") var objective: String
     @Field(key: "measurement_criteria") var measurementCriteria: String
     @Field(key: "achievability_note") var achievabilityNote: String?
     @Field(key: "barriers") var barriers: String?
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - Goal Content Conformance
extension Goal: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case description
        case type
        case targetDate = "target_date"
        case status
        case outcome
        case objective
        case measurementCriteria = "measurement_criteria"
        case achievabilityNote = "achievability_note"
        case barriers
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateGoal: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("goals")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("description", .string, .required)
            .field("type", .string, .required)
            .field("target_date", .date, .required)
            .field("status", .string, .required)
            .field("outcome", .string)
            .field("objective", .string, .required)
            .field("measurement_criteria", .string, .required)
            .field("achievability_note", .string)
            .field("barriers", .string)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("goals").delete()
    }
}


final class Intervention: Model, @unchecked Sendable {
    static let schema = "interventions"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "action") var action: String
    @Field(key: "responsible_party") var responsibleParty: String
    @Field(key: "due_date") var dueDate: Date
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - Intervention Content Conformance
extension Intervention: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case action
        case responsibleParty = "responsible_party"
        case dueDate = "due_date"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateIntervention: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("interventions")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("action", .string, .required)
            .field("responsible_party", .string, .required)
            .field("due_date", .date, .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("interventions").delete()
    }
}


final class Problem: Model, @unchecked Sendable {
    static let schema = "problems"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "icd_code") var icdCode: String? //"I10",
    @Field(key: "description") var description: String //"Essential (primary) hypertension",
    @Field(key: "clinical_note") var clinicalNote: String? //"BP remains elevated despite medication adjustment.",
    @Field(key: "status") var status: String // active | resolved | inactive
    @Field(key: "date_identified") var dateIdentified: Date
    @Field(key: "source") var source: String // EHR import | self-reported | care team
    @Field(key: "confirmed_by") var confirmedBy: String? //"Dr. Smith, MD"

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - Problem Content Conformance
extension Problem: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case icdCode = "icd_code"
        case description
        case clinicalNote = "clinical_note"
        case status
        case dateIdentified = "date_identified"
        case source
        case confirmedBy = "confirmed_by"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateProblem: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("problems")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("description", .string, .required)
            .field("icd_code", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("problems").delete()
    }
}

struct CreateProblemUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("problems")
            .field("clinical_note", .string)
            .field("status", .string, .required, .sql(.default("active")))
            .field("date_identified", .date, .required)
            .field("source", .string, .required, .sql(.default("care team")))
            .field("confirmed_by", .string)
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("problems")
            .deleteField("clinical_note")
            .deleteField("status")
            .deleteField("date_identified")
            .deleteField("source")
            .deleteField("confirmed_by")
            .update()
    }
}


final class CareTeamMember: Model, @unchecked Sendable {
    static let schema = "care_team_members"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "user_id") var userID: UUID?
    @Field(key: "name") var name: String
    @Field(key: "role") var role: String
    @Field(key: "contact_info") var contactInfo: String
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - CareTeamMember Content Conformance
extension CareTeamMember: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case userID = "user_id"
        case name
        case role
        case contactInfo = "contact_info"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateCareTeamMember: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_team_members")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("user_id", .uuid)
            .field("name", .string, .required)
            .field("role", .string, .required)
            .field("contact_info", .string, .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_team_members").delete()
    }
}


final class CarePlanReview: Model, @unchecked Sendable {
    static let schema = "care_plan_reviews"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "review_date") var reviewDate: Date
    @Field(key: "notes") var notes: String?
    @Field(key: "reviewer_name") var reviewerName: String
    @Field(key: "reviewer_role") var reviewerRole: String?
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - CarePlanReview Content Conformance
extension CarePlanReview: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case reviewDate = "review_date"
        case notes
        case reviewerName = "reviewer_name"
        case reviewerRole = "reviewer_role"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateCarePlanReview: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_reviews")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("review_date", .date, .required)
            .field("notes", .string)
            .field("reviewer_name", .string, .required)
            .field("reviewer_role", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_reviews").delete()
    }
}


final class CarePlanService: Model, @unchecked Sendable {
    static let schema = "care_plan_services"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "cbo_name") var cboName: String
    @Field(key: "staff_name") var staffName: String
    @Field(key: "added_by") var addedBy: String
    @Field(key: "status") var status: String // pending, booked, completed, no_show
    @Field(key: "appointment_date") var appointmentDate: Date?
    @Field(key: "outcome_reason_type") var outcomeReasonType: String?
    @Field(key: "outcome_reason_description") var outcomeReasonDescription: String?
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - CarePlanService Content Conformance
extension CarePlanService: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case cboName = "cbo_name"
        case staffName = "staff_name"
        case addedBy = "added_by"
        case status
        case appointmentDate = "appointment_date"
        case outcomeReasonType = "outcome_reason_type"
        case outcomeReasonDescription = "outcome_reason_description"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateCarePlanService: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_services")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("cbo_name", .string, .required)
            .field("staff_name", .string, .required)
            .field("added_by", .string, .required)
            .field("status", .string, .required)
            .field("appointment_date", .datetime)
            .field("outcome_reason_type", .string)
            .field("outcome_reason_description", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_services").delete()
    }
}


final class CarePlanFollowUp: Model, @unchecked Sendable {
    static let schema = "care_plan_follow_ups"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "datetime") var datetime: Date
    @Field(key: "type") var type: String // Phone, In-Person, Email, etc.
    @Field(key: "outcome") var outcome: String // Reached, No Answer, Rescheduled, etc.
    @Field(key: "notes") var notes: String?
    @Field(key: "staff_name") var staffName: String
    @Field(key: "staff_role") var staffRole: String?
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - CarePlanFollowUp Content Conformance
extension CarePlanFollowUp: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case datetime
        case type
        case outcome
        case notes
        case staffName = "staff_name"
        case staffRole = "staff_role"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateCarePlanFollowUp: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_follow_ups")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("datetime", .datetime, .required)
            .field("type", .string, .required)
            .field("outcome", .string, .required)
            .field("notes", .string)
            .field("staff_name", .string, .required)
            .field("staff_role", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_follow_ups").delete()
    }
}

// MARK: - Request DTOs
struct CarePlanCreateRequest: Content {
    let startDate: Date
    let lastReviewed: Date?
    let nextReviewDate: Date?
    let outcome: String?
    let status: String
}
