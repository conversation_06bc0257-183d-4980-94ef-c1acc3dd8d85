//
//  DiagnosesController.swift
//  hmbl-core
//
//  Created by <PERSON> on 6/20/25.
//
import Foundation
import Vapor
import Fluent
import JWTKit

struct DiagnosesController: RouteCollection {
    
    func boot(routes: any Vapor.RoutesBuilder) throws {
        let diagnoses = routes.grouped("api", "members", ":memberID", "diagnoses")
        diagnoses.post(use: createDiagnosis)
        diagnoses.get(use: listDiagnoses)
        
        let diagnosis = routes.grouped("api", "diagnoses", ":diagnosisID")
        diagnosis.get(use: getDiagnosis)
        diagnosis.put(use: updateDiagnosis)
        diagnosis.delete(use: deleteDiagnosis)
    }
    
    // MARK: - CRUD Operations
    
    func createDiagnosis(req: Request) async throws -> Diagnosis {
        var diagnosis = try req.content.decode(Diagnosis.self)
        diagnosis.$member.id = try req.parameters.require("memberID", as: UUID.self)
        try await diagnosis.save(on: req.db)
        return diagnosis
    }
    
    func listDiagnoses(req: Request) async throws -> [Diagnosis] {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        return try await Diagnosis.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .sort(\.$dateIdentified, .descending)
            .all()
    }
    
    func getDiagnosis(req: Request) async throws -> Diagnosis {
        let id = try req.parameters.require("diagnosisID", as: UUID.self)
        guard let diagnosis = try await Diagnosis.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        return diagnosis
    }
    
    func updateDiagnosis(req: Request) async throws -> Diagnosis {
        let id = try req.parameters.require("diagnosisID", as: UUID.self)
        let input = try req.content.decode(Diagnosis.self)
        guard let diagnosis = try await Diagnosis.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        
        diagnosis.icdCode = input.icdCode
        diagnosis.description = input.description
        diagnosis.clinicalNote = input.clinicalNote
        diagnosis.status = input.status
        diagnosis.dateIdentified = input.dateIdentified
        diagnosis.source = input.source
        diagnosis.confirmedBy = input.confirmedBy
        
        try await diagnosis.update(on: req.db)
        return diagnosis
    }
    
    func deleteDiagnosis(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("diagnosisID", as: UUID.self)
        guard let diagnosis = try await Diagnosis.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await diagnosis.delete(on: req.db)
        return .noContent
    }
    
    // MARK: - Helper Methods
    
    func getDiagnosesByStatus(req: Request) async throws -> [Diagnosis] {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        let status = try req.query.get(String.self, at: "status")
        
        return try await Diagnosis.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .filter(\.$status == status)
            .sort(\.$dateIdentified, .descending)
            .all()
    }
    
    func getActiveDiagnoses(req: Request) async throws -> [Diagnosis] {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        
        return try await Diagnosis.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .filter(\.$status == "active")
            .sort(\.$dateIdentified, .descending)
            .all()
    }
}
