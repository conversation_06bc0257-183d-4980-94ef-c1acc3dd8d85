//
//  MedicationsController.swift
//  hmbl-core
//
//  Created by <PERSON> on 6/20/25.
//
import Foundation
import Vapor
import Fluent
import JWTKit

struct MedicationsController: RouteCollection {
    
    func boot(routes: any Vapor.RoutesBuilder) throws {
        let medications = routes.grouped("api", "members", ":memberID", "medications")
        medications.post(use: createMedication)
        medications.get(use: listMedications)
        
        let medication = routes.grouped("api", "medications", ":medicationID")
        medication.get(use: getMedication)
        medication.put(use: updateMedication)
        medication.delete(use: deleteMedication)
        
        // Additional routes for medication management
        let memberMeds = routes.grouped("api", "members", ":memberID", "medications")
        memberMeds.get("active", use: getActiveMedications)
        memberMeds.get("by-type", use: getMedicationsByType)
        memberMeds.post(":medicationID", "discontinue", use: discontinueMedication)
    }
    
    // MARK: - CRUD Operations
    
    func createMedication(req: Request) async throws -> Medication {
        var medication = try req.content.decode(Medication.self)
        medication.$member.id = try req.parameters.require("memberID", as: UUID.self)
        try await medication.save(on: req.db)
        return medication
    }
    
    func listMedications(req: Request) async throws -> [Medication] {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        return try await Medication.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .sort(\.$startDate, .descending)
            .all()
    }
    
    func getMedication(req: Request) async throws -> Medication {
        let id = try req.parameters.require("medicationID", as: UUID.self)
        guard let medication = try await Medication.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        return medication
    }
    
    func updateMedication(req: Request) async throws -> Medication {
        let id = try req.parameters.require("medicationID", as: UUID.self)
        let input = try req.content.decode(Medication.self)
        guard let medication = try await Medication.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        
        medication.medicationName = input.medicationName
        medication.rxNormCode = input.rxNormCode
        medication.dosage = input.dosage
        medication.route = input.route
        medication.frequency = input.frequency
        medication.startDate = input.startDate
        medication.endDate = input.endDate
        medication.prescribedBy = input.prescribedBy
        medication.status = input.status
        medication.adherenceNotes = input.adherenceNotes
        medication.source = input.source
        medication.medicationType = input.medicationType
        
        try await medication.update(on: req.db)
        return medication
    }
    
    func deleteMedication(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("medicationID", as: UUID.self)
        guard let medication = try await Medication.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await medication.delete(on: req.db)
        return .noContent
    }
    
    // MARK: - Helper Methods
    
    func getActiveMedications(req: Request) async throws -> [Medication] {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        
        return try await Medication.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .filter(\.$status == "active")
            .sort(\.$startDate, .descending)
            .all()
    }
    
    func getMedicationsByType(req: Request) async throws -> [Medication] {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        let medicationType = try req.query.get(String.self, at: "type")
        
        return try await Medication.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .filter(\.$medicationType == medicationType)
            .sort(\.$startDate, .descending)
            .all()
    }
    
    func discontinueMedication(req: Request) async throws -> Medication {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        let medicationID = try req.parameters.require("medicationID", as: UUID.self)
        
        guard let medication = try await Medication.query(on: req.db)
            .filter(\.$id == medicationID)
            .filter(\.$member.$id == memberID)
            .first() else {
            throw Abort(.notFound)
        }
        
        medication.status = "discontinued"
        medication.endDate = Date()
        
        try await medication.update(on: req.db)
        return medication
    }
}

// MARK: - Request/Response DTOs
struct MedicationCreateRequest: Content {
    let medicationName: String
    let rxNormCode: String?
    let dosage: String
    let route: String
    let frequency: String
    let startDate: Date
    let prescribedBy: String
    let adherenceNotes: String?
    let source: String
    let medicationType: String
}

struct MedicationResponse: Content {
    let id: UUID?
    let medicationName: String
    let rxNormCode: String?
    let dosage: String
    let route: String
    let frequency: String
    let startDate: Date
    let endDate: Date?
    let prescribedBy: String
    let status: String
    let adherenceNotes: String?
    let source: String
    let medicationType: String
    let createdAt: Date?
    let updatedAt: Date?
    
    init(from medication: Medication) {
        self.id = medication.id
        self.medicationName = medication.medicationName
        self.rxNormCode = medication.rxNormCode
        self.dosage = medication.dosage
        self.route = medication.route
        self.frequency = medication.frequency
        self.startDate = medication.startDate
        self.endDate = medication.endDate
        self.prescribedBy = medication.prescribedBy
        self.status = medication.status
        self.adherenceNotes = medication.adherenceNotes
        self.source = medication.source
        self.medicationType = medication.medicationType
        self.createdAt = medication.createdAt
        self.updatedAt = medication.updatedAt
    }
}
