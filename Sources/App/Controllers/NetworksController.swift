//
//  File.swift
//  
//
//  Created by <PERSON> on 2/3/23.
//

import Foundation
import Vapor
import Fluent


struct NetworkQuery: Content {
    var name:String?
    var type:String?
    var carrier:String?
    var city:String?
    var state:String?
    var zip:String?
    var search:String?
}

struct ConnnectNetworkResponse: Content {
    var networkes: [Network]
    var connected: [OrgGraphQl]
}

struct SchedulerInput: Content {
    var ids:[String]
}

struct NetworksController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("networks")
        
        users.get(use: index)        
        users.get("page", use: pageIndex)
        users.get(":netID", use: lookup)
        users.get("connected", use: connected)
        users.post("scheduler", use: scheduler)
        users.post("availableTimes", use: schedulerMoreTimes)
        users.post("book", use: schedulerBook)
        users.post("cancel", use: cancelAppointment)
        
             
        users.post(use: create)
        
        users.put(":netID", use: update)
        users.put([":netID", "service"], use: attachServices)
                
        users.delete(":netID", use: delete)
    }
    
    //MARK - Fetch
    func lookup(req: Request) throws -> EventLoopFuture<Network> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .networks) }
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .networks)}
        return Network.query(on: req.db)
            .filter(\.$id == networkID)
            .with(\.$address)
            .with(\.$phones)
            .with(\.$services)
            .with(\.$carriers)
            .first().unwrap(or:  Abort(.notFound))
    }
        
    static func find(req: Request, id:String?) throws -> EventLoopFuture<Network> {
        guard let id = id else { throw NetworkError.error(type: .networks)}
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .networks)}
        return Network.find(networkID, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw NetworkError.error(type: .networks) }
            return foundModel
        }
    }
    
    static func query(req: Request, id:String?) throws -> EventLoopFuture<Network> {
        guard let id = id else { throw NetworkError.error(type: .networks)}
        guard let networkID = UUID(id) else { throw NetworkError.error(type: .networks)}
        return Network.query(on: req.db)
            .filter(\.$id == networkID)
            .with(\.$address)
            .with(\.$phones)
            .with(\.$carriers)
            .first().unwrap(or:  Abort(.notFound))
    }
    
    func index(req: Request) throws -> EventLoopFuture<[Network]> {
        let org:String? = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return self.buildQuery(query: Network.query(on: req.db), req: req, orgID: orgID).all()
    }
    
    func pageIndex(req: Request) throws -> EventLoopFuture<Page<Network>> {
        let org:String? = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return self.buildQuery(query: Network.query(on: req.db), req: req, orgID: orgID).paginate(for:req)
    }

    
    //MARK: - Create
    func create(req: Request) throws -> EventLoopFuture<Network> {
        let input = try req.content.decode(NetworkCreateInput.self)
        let network = input.network()
        return try! OrgsController.find(req: req, id: input.orgID).flatMap { org in
            return org.$networks.create(network, on: req.db).transform(to: network).flatMap { network in
                return try! NetworksController.query(req: req, id: network.id?.uuidString ?? "").flatMap { network in
                    return try! self.updateAddressIfNeeded(req: req, input: input, network: network).flatMap { network in
                        return try! self.updatePhoneIfNeeded(req: req, input: input, network: network).flatMap({ _ in
                            return try! handleCarriers(req: req, input: input, network: network).flatMap({ network in
                                if input.hasInvites {
                                    return try! GraphQLController.kioskLogin(req: req).flatMap { response in
                                        let token  = response.login.user?.token ?? ""
                                        return try! GraphQLController.createOrg(token: token,
                                                                                req: req,
                                                                                input: input.schedulerInput,
                                                                                network: network,
                                                                                emails: input.invites ?? []).flatMap({ _ in
                                            return try! NetworksController.query(req: req, id: network.id?.uuidString ?? "")
                                        })
                                    }
                                } else {
                                    return req.eventLoop.future(network)
                                }
                            })
                        })
                    }
                }
            }
        }
    }
    
    
    //MARK: - Update
    func update(req: Request) throws -> EventLoopFuture<Network> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .networks) }
        let input = try req.content.decode(NetworkCreateInput.self)
        return try NetworksController.query(req: req, id: id).flatMap { network in
            let network = input.returnUpdatedModel(network: network)
            return network.update(on: req.db).transform(to: network).flatMap { network in
                return try! handleServices(req: req, input: input, network: network).flatMap({ network in
                    return try! handleCarriers(req: req, input: input, network: network)
                })
            }
        }
    }
    
    fileprivate func handleCarriers(req: Request, input: NetworkCreateInput, network: Network) throws -> EventLoopFuture<Network> {
        if let carriers = input.carriers {
            if carriers.isEmpty {
                return network.$carriers.detachAll(on: req.db).flatMap { _ in
                    return try! NetworksController.query(req: req, id: network.id?.uuidString)
                }
            } else {
                return try! attachDetachCarriers(req: req, carriers: carriers, network: network).flatMap({ _ in
                    return try! NetworksController.query(req: req, id: network.id?.uuidString)
                })
            }
        } else {
            return req.eventLoop.future(network)
        }
    }
    
    fileprivate func handleServices(req: Request, input: NetworkCreateInput, network: Network) throws -> EventLoopFuture<Network> {
        if let services = input.services {
            return try! attachDetachServices(req: req, services: services, network: network).flatMap { _ in
                return try! self.updateAddressIfNeeded(req: req, input: input, network: network).flatMap { network in
                    return try! self.updatePhoneIfNeeded(req: req, input: input, network: network)
                }
            }
        } else {
            return try! self.updateAddressIfNeeded(req: req, input: input, network: network).flatMap { network in
                return try! self.updatePhoneIfNeeded(req: req, input: input, network: network)
            }
        }
    }
    
    fileprivate func attachDetachCarriers(req:Request, carriers:[String], network:Network) throws -> EventLoopFuture<Network> {
        return try!CarriersController.findAll(req: req, ids: carriers).flatMap { allCarriers in
            if allCarriers.isEmpty {
                return req.eventLoop.future(network)
            } else {
                return network.$carriers.detachAll(on: req.db).flatMap { _ in
                    return network.$carriers.attach(allCarriers, on: req.db).transform(to: network)
                }
            }
        }
    }
    
    fileprivate func attachDetachServices(req:Request, services:[String], network:Network) throws -> EventLoopFuture<Network> {
        return try!ServicesController.findAll(req: req, ids: services).flatMap { allServices in
            if allServices.isEmpty {
                return network.$services.detachAll(on: req.db).transform(to: network)
            } else {
                return network.$services.detachAll(on: req.db).flatMap { _ in
                    return network.$services.attach(allServices, on: req.db).transform(to: network)
                }
            }
        }
    }
    
    fileprivate func updatePhoneIfNeeded(req:Request, input:NetworkCreateInput, network:Network) throws -> EventLoopFuture<Network> {
        if let phoneInput = input.phone {
            let phone = phoneInput.phone()
            return network.$phones.create(phone, on: req.db).transform(to: network)
        } else {
            return req.eventLoop.future(network)
        }
    }
    
    fileprivate func updateAddressIfNeeded(req:Request, input:NetworkCreateInput, network:Network) throws -> EventLoopFuture<Network> {
        return try AddressService.updateAddressIfNeeded(req: req, input: input.address, model: network).transform(to: network)
    }
    
    func attachServices(req: Request) throws -> EventLoopFuture<Network> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .networks) }
        let input = try req.content.decode(NetworkCreateInput.self)
        guard let allServices  = input.services else { throw NetworkError.error(type: .badRequest, msg: "Services are required.") }
        return try NetworksController.find(req: req, id: id).flatMap { network in
            return try! ServicesController.findAll(req: req, ids: allServices).flatMap { services in
                return network.$services.attach(services, on: req.db).transform(to: network)
            }
        }
    }
    
    //MARK: - Delete
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get("netID") else { throw NetworkError.error(type: .networks) }
        return try NetworksController.find(req: req, id: id).flatMap { network in
            return network.delete(on: req.db).transform(to: HTTPStatus.accepted)
        }
    }
    
    
//MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<Network>, req:Request, orgID:String) -> QueryBuilder<Network> {
        let queryParams = try? req.query.decode(NetworkQuery.self)
        
        
        if let city = queryParams?.city.nonEmptyValue {
            query.filter(Address.self, \.$city, .custom("ilike"), "%\(city.lowercased())%")
        }
        
        if let st = queryParams?.state.nonEmptyValue {
            query.filter(Address.self, \.$state == st)
        }
        
        if let zipCode = queryParams?.zip.nonEmptyValue {
            query.filter(Address.self, \.$zip == zipCode)
        }
        
        if let type = queryParams?.type.nonEmptyValue {
            query.join(siblings: \.$services)
                .group(.or) { orGroup in
                    orGroup.filter(Service.self, \.$type == type)
                }
        }
        
        if let carrier = queryParams?.carrier.nonEmptyValue {
            query.join(siblings: \.$carriers)
                .filter(Carrier.self, \.$name, .custom("ilike"), "%\(carrier.lowercased())%")
        }
        
        if let nm = queryParams?.name.nonEmptyValue  {
            query.filter(\.$name, .custom("ilike"), "%\(nm.lowercased())%")
        }
        
        if let search = queryParams?.search.nonEmptyValue  {
            query.filter(\.$name, .custom("ilike"), "%\(search.lowercased())%")
        }
 
        return query
            .with(\.$services)
            .with(\.$address)
            .with(\.$phones)
            .with(\.$carriers)
            .filter(Network.self, \.$org.$id == UUID(uuidString: orgID)!)
            .join(Address.self, on: \Address.$network.$id == \Network.$id)
    }
}


//MARK: - Connected Network
extension NetworksController {
    
    func scheduler(req: Request) throws -> EventLoopFuture<[OrgGraphQl]> {
        let ids = try req.content.decode(SchedulerInput.self).ids
        let futures = ids.map { id in
            do {
                return try GraphQLController.fetchScheduler(req: req, id: id).flatMap { response in
                    return req.eventLoop.future(response.data.org)
                }
            } catch {
                return req.eventLoop.makeFailedFuture(error) // Ensure errors don’t crash the app
            }
        }
        return req.eventLoop.flatten(futures)
    }
    
    func schedulerMoreTimes(req: Request) throws -> EventLoopFuture<AppointmentGraphqlResponse> {
        let input = try req.content.decode(ProviderTimeInput.self)
        return try! GraphQLController.fetchAppointmentTimes(req: req, input: input)
    }
    
    func schedulerBook(req: Request) throws -> EventLoopFuture<ClientResponse> {
        var input = try req.content.decode(AppointmentCreateInput.self)
        return try! MembersController.find(req: req, id: input.person).flatMap { member in
            if let schedulerMemberId = member.schedulerMemberId {
                input.person = schedulerMemberId
                return try! GraphQLController.createAppointment(req: req, input: input)
            } else {
                return try! GraphQLController.createAppointment(req: req, input: input, userFindOrCreate: member)
            }
        }
    }
    
    func fetchSchedulers(req: Request, ids: [String]) throws -> EventLoopFuture<[SchedulerResponse]> {
        let futures = ids.map { id in
            do {
                return try GraphQLController.fetchScheduler(req: req, id: id)
            } catch {
                return req.eventLoop.makeFailedFuture(error) // Ensure errors don’t crash the app
            }
        }
        return req.eventLoop.flatten(futures)
    }
    
    func connectedQuery(req:Request, orgId: String, type: String) throws -> EventLoopFuture<[Network]> {
        return Network.query(on: req.db)
            .with(\.$address)
            .with(\.$phones)
            .with(\.$services)
            .with(\.$carriers)
            .filter(\.$schedulerId != nil)
            .filter(.custom("types @> '{\(type)}'::TEXT[]"))
            .filter(Network.self, \.$org.$id == UUID(uuidString: orgId)!)
            .all()
    }
    
    struct IDInput: Content {
        let id: String
    }
    
    func cancelAppointment(req: Request) throws -> EventLoopFuture<CancelAptResponseData> {
        let input = try req.content.decode(IDInput.self)
        return try GraphQLController.cancelApt(req: req, id: input.id)
    }
    
    func connected(req: Request) throws -> EventLoopFuture<[Network]> {
        let org:String? = req.query["org"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        return self.buildQuery(query: Network.query(on: req.db), req: req, orgID: orgID).all().flatMap { allNetworks in
            let type:String? = req.query["type"]
            return try! connectedQuery(req: req, orgId: orgID, type: type ?? "").flatMap({ connectedNetworks in
                let ids = connectedNetworks.compactMap({$0.schedulerId})
                if ids.isEmpty {
//                    return req.eventLoop.future(ConnnectNetworkResponse(networkes: allNetworks, connected: []))
                    return req.eventLoop.future(allNetworks)
                } else {
                    let setData = Array(Set(allNetworks + connectedNetworks))
                    return req.eventLoop.future(setData)
//                    return try! fetchSchedulers(req: req, ids: ids).flatMap { response in
//                        let orgs = response.map({$0.data.org})
//                        return req.eventLoop.future(ConnnectNetworkResponse(networkes: allNetworks, connected: orgs))
//                    }
                }
            })
        }
    }
}
