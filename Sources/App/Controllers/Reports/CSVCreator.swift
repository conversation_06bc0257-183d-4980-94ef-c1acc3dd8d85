//
//  File.swift
//
//
//  Created by <PERSON> on 1/30/24.
//

import Foundation
import Fluent
import Vapor

struct CSVCreator {
    
    func csvEscapeRow(_ values: [String]) -> String {
        return values.map { csvEscape($0) }.joined(separator: ",") + "\n"
    }
    
    func combinedAnswerQuestionRow(answer: Answer, question: Question?) -> String {
        let section = question?.section ?? question?.question?.section
        let survey = section?.survey

        return csvEscapeRow([
            answer.id?.uuidString ?? "",
            answer.takerID ?? "",
            answer.giverID ?? "",
            answer.answer,
            answer.value,
            answer.score,
            Date.createdAtString(date:answer.createdAt),
            answer.questionID ?? "",
            question?.title ?? "",
            question?.message ?? "",
            question?.value.description ?? "",
            question?.score?.description ?? "",
            question?.type?.rawValue ?? "",
            section?.title ?? "",
            survey?.id?.uuidString ?? "",
            survey?.name ?? "",
            survey?.key ?? ""
        ])
    }
    
    func buildRow(member:Member) -> String {
        let headOfHouse = member.headOfHouse?.id != nil ? "yes" : ""
        return "\(member.email),\(member.firstName),\(member.middleName ?? ""),\(member.lastName),\(member.type),\(member.roles),\(member.dob),\(member.color ?? ""),\(member.gender ?? ""),\(member.genderIdentity ?? ""),\(member.ethnicity ?? ""),\(member.sexualIdentity ?? ""),\(member.pronouns ?? ""),\(member.lang ?? ""),\(member.referredBy ?? ""),\(member.lastAt ?? ""),\(member.status ?? ""),\(member.score ?? ""),\(member.refId ?? ""),\(headOfHouse),\(Date.createdAtString(date: member.createdAt)),\(Date.createdAtString(date: member.updatedAt))\n"
    }
    
    func buildTestHeaders() -> String {
        return "email,firstName,middleName,lastName,type,roles,dob,color,gender,ethnicity,sexualIdentity,genderIdentity,pronouns,lang,referredBy,lastAt,status,score,refId,head of house,created at,updated at\n"
    }
    
    func addressHeaders() -> String {
        "id,street,street2,city,state,zip,country,county,kind,lat,lon,note,created at,updated at\n"
    }
    
    func addressRow(address: Address) -> String {
        "\(address.id  ?? UUID()),\(address.street),\(address.street2 ?? ""),\(address.city),\(address.state),\(address.zip),\(address.country),\(address.county),\(address.kind),\(address.lat ?? ""),\(address.lon ?? ""),\(address.note ?? ""),\(Date.createdAtString(date: address.createdAt)),\(Date.createdAtString(date: address.updatedAt))\n"
    }
    
    func phoneHeaders() -> String {
        return "id,label,number,created at,updated at,created at,updated at\n"
    }
    
    func phoneRow(phone: PhoneNumber) -> String {
        return "\(phone.id ?? UUID()),\(phone.label),\(phone.number),\(Date.createdAtString(date: phone.createdAt)),\(Date.createdAtString(date: phone.updatedAt))\n"
    }
    
    func networksWithServicesHeaders() -> String {
        return "name,services count\n"
    }
    
    func networksWithServices(network: Network) -> String {
        let services = network.services.count
        return "\(network.name),\(services)\n"
    }
    
    func carepackageItemHeaders() -> String {
        return "netId,name,type,status,desc,created at,updated at\n"
    }
    
    func packageItemRow(item: CarePackageItem) -> String {
        let network = item.network
        return "\(network?.id ?? UUID()),\(network?.name ?? ""),\(item.type),\(item.status),\(item.desc ?? "-"),\(Date.createdAtString(date: item.createdAt)),\(Date.createdAtString(date: item.updatedAt))\n"
    }
    
    func surveyHeaders() -> String {
        return "id,title,memberId,status,score,key,startedAt,endedAt,baseline,lastTaken,created_at,updated_at\n"
    }
    
    func surveyRow(survey: Survey) -> String {
        return "\(survey.id ?? UUID()),\(survey.name),\(survey.memberID?.uuidString ?? ""),\(survey.status),\(survey.score ?? ""),\(survey.key),\(Date.createdAtString(date: survey.startedAt) ),\(Date.createdAtString(date: survey.endedAt) ),\(survey.firstSurvey ?? 0),\(survey.lastSurvey ?? 0),\(Date.createdAtString(date: survey.createdAt) ),\(Date.createdAtString(date: survey.updatedAt) )\n"
    }
    
    func sectionHeaders() -> String {
        return "title,complete,score,type,kind,created_at,updated_at\n"
    }
    
    func sectionRow(section: Section) -> String {
        return "\(section.id ?? UUID()),\(section.title),\(section.complete),\(section.score ?? ""),\(section.type ?? ""),\(section.kind ?? ""),\(Date.createdAtString(date: section.createdAt)),\(Date.createdAtString(date: section.updatedAt))\n"
    }
    
    
    func questionHeaders() -> String {
        return "title,message,level,value,score,type,createdAt,updatedAt\n"
    }
    
    func questionRow(question: Question, newLine: Bool = true) -> String {
        let id =    question.id?.uuidString ?? ""
        let title = csvEscape(question.title ?? "")
        let msg =   csvEscape(question.message.clean())
        let score = question.score ?? -99
        let sectionId = question.section?.id?.uuidString ?? "-"
        let questionId = question.question?.id?.uuidString ?? "-"
        let type = question.type ?? .none
        
        let string = "\(id),\(title.clean()),\(msg),\(question.value),\(score),\(sectionId),\(questionId),\(type),\(Date.createdAtString(date: question.createdAt)),\(Date.createdAtString(date: question.updatedAt))"
        return appendNewLineFor(item: string, newLine: newLine)
    }
    
    func householdHeaders() -> String {
        return "id,title,type,kind,lastVisit,householdScore,street,street2,city,state,zip,lat,lon,createdAt,updatedAt\n"
    }
    
    func householdRow(household: Household) -> String {
        let address = household.address.first(where: {$0.kind == "main"})
        return "\(household.id ?? UUID()),\(household.title),\(household.type),\(household.kind),\(household.lastVisit ?? ""),\(household.householdScore ?? ""),\(address?.street ?? ""),\(address?.street2 ?? ""),\(address?.city ?? ""),\(address?.state ?? ""),\(address?.zip ?? ""),\(address?.lat ?? ""),\(address?.lon ?? ""),\(Date.createdAtString(date: household.createdAt)),\(Date.createdAtString(date: household.updatedAt))\n"
    }
    
    func answerHeaders() -> String {
        return "id,surveyId,navigatorId,memberId,answer,questionId,value,score,key,createdAt,updatedAt\n"
    }
    
    
    func networkRow(network: Network) -> String {
        return networksString(network: network)
    }
        
    func answerRow(answer: Answer, newLine: Bool = true) -> String {
        return answersString(answer: answer, newLine: newLine)
        
//        return "\(answer.id ?? UUID()),\(answer.surveyID ?? ""),\(answer.takerID ?? ""),\(answer.giverID ?? ""),\(answer.answer),\(answer.questionID ?? ""),\(answer.value),\(answer.score),\(answer.key ?? ""),\(Date.createdAtString(date: answer.createdAt)),\(Date.createdAtString(date: answer.updatedAt))\n"
    }
    
    
    func memberHeaders() -> String {
        return "id,email,first,middle,last,type,dob,gender,ethnicity,sexualIdentity,genderIdentity,pronouns,language,referredBy,lastAt,status,score,refId,createdAt,updatedAt\n"
    }
    
    func memberRow(member: Member) -> String {
        return "\(member.id ?? UUID()),\(member.email),\(member.firstName),\(member.middleName ?? ""),\(member.lastName),\(member.type),\(member.dob),\(member.gender ?? ""),\(member.ethnicity ?? ""),\(member.sexualIdentity ?? ""),\(member.genderIdentity ?? ""),\(member.pronouns ?? ""),\(member.lang ?? ""),\(member.referredBy ?? ""),\(member.lastAt ?? ""),\(member.status ?? ""),\(member.score ?? ""),\(member.refId ?? ""),\(Date.createdAtString(date: member.createdAt)),\(Date.createdAtString(date: member.updatedAt))\n"
    }
    
    func memberRowWithAddressAnTeamAndPhone(member: Member) -> String {
        let household = member.households.last
        let team = household?.teams.last
        let householdId = household?.id?.uuidString ?? "-"
        let householdName = household?.title ?? "-"
        let teamId = team?.id?.uuidString ?? "-"
        let teamName = team?.name ?? "-"
        
        let phone = member.phones.last
        let address = household?.address.last
        
        if let address, let phone {
            
            return "\(memberString(member: member, householdId: householdId, householdName: householdName,newLine: false)),\(teamId),\(teamName),\(addressString(address: address, phone: phone, newLine: false))"
            
        } else if let address {

            return "\(memberString(member: member, householdId: householdId, householdName: householdName,newLine: false)),\(teamId),\(teamName),\(addressString(address: address, phone: nil, newLine: false))"
            
        }  else if let phone {
            
            return "\(memberString(member: member, householdId: householdId, householdName: householdName,newLine: false)),\(teamId),\(teamName),\(addressString(address: nil, phone: phone, newLine: false))"
            
        } else {
            return "\(memberString(member: member, householdId: householdId, householdName: householdName,newLine: false)),\(teamId),\(teamName)\n"
        }
    }
    
    func memberRowWithAddress(member: Member) -> String {
        let household = member.households.last
        let team = household?.teams.last
        let householdId = household?.id?.uuidString ?? "-"
        let householdName = household?.title ?? "-"
        let teamId = team?.id?.uuidString ?? "-"
        let teamName = team?.name ?? "-"
        if let address = member.address.first{
            return "\(memberString(member: member, householdId: householdId, householdName: householdName)),\(teamId),\(teamName),\(addressString(address: address, phone: nil, newLine: false))"
        } else {
            return memberString(member: member, householdId: householdId, householdName: householdName)
        }
    }
    
    func notesRowWithTags(note: Note) -> String {
        return noteString(note: note)
    }
    
    fileprivate func addressString(address: Address?, phone: PhoneNumber?, newLine: Bool = true) -> String {
        var components: [String] = []

        if let address {
            components.append(address.id?.uuidString ?? "")
            components.append(address.street)
            components.append(address.street2 ?? "")
            components.append(address.city)
            components.append(address.state)
            components.append(address.zip)
            components.append(address.country)
            components.append(address.county)
            components.append(address.kind)
            components.append(address.lat ?? "")
            components.append(address.lon ?? "")
            components.append(address.note ?? "")
        } else {
            // Pad with empty strings to keep CSV structure
            components = Array(repeating: "-", count: 12)
        }

        if let phone, !phone.number.isEmpty {
            components.append("\(phone.number)")
        }

        var csvLine = components.map { csvEscape($0) }.joined(separator: ",")
        csvLine = "\(csvLine)\n"
        return appendNewLineFor(item: csvLine, newLine: newLine)
    }
    
    fileprivate func networksString(network: Network, newLine: Bool = true) -> String {
        let string = "\(network.id?.uuidString  ?? "")),\(network.name),\(Date.createdAtString(date: network.createdAt)),\(Date.createdAtString(date: network.updatedAt))"
        return appendNewLineFor(item: string, newLine: newLine)
    }
    
    fileprivate func answersString(answer: Answer, newLine: Bool = true) -> String {
        let string = "\(answer.id?.uuidString  ?? ""),\(csvEscape(answer.answer.sanitizeForCSV())),\(answer.surveyID ?? ""),\(answer.takerID ?? ""),\(answer.parentID ?? ""),\(answer.questionID ?? ""),\(csvEscape(answer.value.sanitizeForCSV())),\(answer.score),\(answer.key ?? ""),\(Date.createdAtString(date: answer.createdAt)),\(Date.createdAtString(date: answer.updatedAt))\n"
        return appendNewLineFor(item: string, newLine: newLine)
    }
    
    
    fileprivate func memberString(member: Member, householdId: String, householdName: String, newLine: Bool = true) -> String {
        let string = "\(member.id ?? UUID()),\(member.email),\(member.firstName),\(member.middleName ?? ""),\(csvEscape(member.lastName)),\(member.type),\(member.dob),\(member.gender ?? ""),\(member.ethnicity ?? ""),\(member.sexualIdentity ?? ""),\(member.genderIdentity ?? ""),\(member.pronouns ?? ""),\(member.lang ?? ""),\(csvEscape(member.referredBy ?? "")),\(member.lastAt ?? ""),\(member.status ?? ""),\(member.score ?? ""),\(member.refId ?? ""),\(member.pregnancyStatus ?? ""),\(member.deliveryDate ?? ""),\(member.military ?? ""),\(member.enrolledOn  ?? ""),\(member.unenrolledDate ?? ""),\(Date.createdAtString(date: member.createdAt)),\(Date.createdAtString(date: member.updatedAt)),\(householdId),\(csvEscape(householdName))"
        return appendNewLineFor(item: string, newLine: newLine)
    }
    
    fileprivate func noteString(note: Note, newLine: Bool = true) -> String {
        let msg = csvEscape(note.msg.clean())
        let title = csvEscape(note.title)
        let subtitle = csvEscape(note.subtitle ?? "")
        let id = note.id?.uuidString ?? ""
        let string = "\(id),\(title),\(subtitle),\(note.type),\(msg),\(Date.createdAtString(date: note.createdAt)),\(Date.createdAtString(date: note.updatedAt)), \(note.member?.id?.uuidString ?? ""),\(note.member?.pregnancyStatus ?? ""),\(note.member?.deliveryDate ?? "")"
        return appendNewLineFor(item: string, newLine: newLine)
    }
}

func appendNewLineFor(item:String, newLine: Bool = true) -> String {
    var string = item
    if newLine {
        string.append("\n")
        return string
    } else {
        return string
    }
}

enum AnswersHeaders: String, CaseIterable {
    case id = "id"
    case answer = "answer"
    case survey = "survey"
    case memberId = "memberId"
    case parentId = "parentId"
    case questionId = "questionId"
    case value = "value"
    case score = "score"
    case key = "key"
    case createdAt = "createdAt"
    case updatedAt = "updatedAt"
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
}

enum NetworkHeaders: String, CaseIterable {
    case id = "id"
    case network = "network"
    case createdAt = "createdAt"
    case updatedAt = "updatedAt"
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
}

enum NotesHeaders: String, CaseIterable {
    case id = "id"
    case title = "title"
    case subtitle = "subtitle"
    case type = "type"
    case msg = "msg"
    case createdAt = "createdAt"
    case updatedAt = "updatedAt"
    
    //empowered case
    case memberId = "memberId"
    case member = "member"
    case team = "team"
    case pregnancyStatus = "pregnancyStatus"
    case deliveryDate = "deliveryDate"
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
}




enum InsurancePolicyCSV: String, CaseIterable {
    case policyId
    case policyNumber
    case startDate
    case endDate
    case userId
    case planType
    case planName
    case carrierId
    case issuer
    case type
    case createdAt
    case updatedAt
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func row(policy: InsurancePolicy) -> String {
        let plan = policy.plans.last
        let issuer = plan?.issuer ?? ""
        let type = plan?.type ?? ""
        
        return "\(policy.id ?? UUID()),\(policy.policyNumber),\(policy.startDate),\(policy.endDate ?? ""),\(policy.userId),\(policy.planType),\(policy.planName),\(policy.carrierId),\(issuer),\(type),\(Date.createdAtString(date: policy.createdAt)),\(Date.createdAtString(date: policy.updatedAt))\n"
    }
}

enum InsuranceCardCSV: String, CaseIterable {
    case id
    case policyholderName
    case dateOfBirth
    case policyholderID
    case effectiveDate
    case expirationDate
    case groupNumber
    case createdAt
    case updatedAt
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func headersWithPolicy() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        let policyHeaders = InsurancePolicyCSV.headers()
        print(policyHeaders)
        return "\(header),\(policyHeaders)"
    }
    
    static func row(card: InsuranceCard, policy: InsurancePolicy) -> String {
        let policyRow = InsurancePolicyCSV.row(policy: policy)
        return "\(card.id ?? UUID()),\(card.policyholderName),\(card.dateOfBirth),\(card.policyholderID),\(card.effectiveDate),\(card.expirationDate ?? ""),\(card.groupNumber ?? ""),\(Date.createdAtString(date: card.createdAt)),\(Date.createdAtString(date: card.updatedAt)),\(policyRow)" //contains new line
    }
    
    static func row(card: InsuranceCard) -> String {
//        let address = household.address.first(where: {$0.kind == "main"})
        return "\(card.id ?? UUID()),\(card.policyholderName),\(card.dateOfBirth),\(card.policyholderID),\(card.effectiveDate),\(card.expirationDate ?? ""),\(card.groupNumber ?? ""),\(Date.createdAtString(date: card.createdAt)),\(Date.createdAtString(date: card.updatedAt))\n"
    }
}


enum Households: String, CaseIterable {
    case id
    case title
    case type
    case kind 
    case lastVisit
    case householdScore
    case street
    case street2
    case city
    case state
    case zip
    case lat
    case lon
    case createdAt
    case updatedAt
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
}

enum CarePackageCSV: String, CaseIterable {
    case id
    case title
    case status
    case type
    case navigatorId
    case memberId
    case startedAt
    case endedAt
    case reasonKey
    case reasonMsg
    case createdAt
    case updatedAt
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func row(plan: CarePackage) -> String {
        let id = plan.id?.uuidString ?? ""
        let title = plan.title
        let reasonKey = plan.reason?.reason ?? ""
        let reasonMsg = plan.reason?.msg ?? ""
        let navigatorId = plan.creator ?? ""
        let memberId = plan.reciever ?? ""
        let startedAt = plan.startedAt ?? plan.createdAt
        return "\(id),\(title.clean()),\(plan.status),\(plan.type),\(navigatorId),\(memberId),\(Date.createdAtString(date: startedAt) ),\(Date.createdAtString(date: plan.endedAt) ),\(reasonKey),\(reasonMsg.clean()),\(Date.createdAtString(date: plan.createdAt) ),\(Date.createdAtString(date: plan.updatedAt) )\n"
    }
}

enum SurveyCSV: String, CaseIterable {
    case id
    case name
    case memberID
    case status
    case score
    case key
    case startedAt
    case endedAt
    case firstSurvey
    case lastSurvey
    case createdAt
    case updatedAt
        
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func row(survey: Survey) -> String {
        let id = survey.id?.uuidString ?? ""
        return "\(id),\(survey.name),\(survey.memberID?.uuidString ?? ""),\(survey.status),\(survey.score ?? ""),\(survey.key),\(Date.createdAtString(date: survey.startedAt) ),\(Date.createdAtString(date: survey.endedAt) ),\(survey.firstSurvey ?? 0),\(survey.lastSurvey ?? 0),\(Date.createdAtString(date: survey.createdAt) ),\(Date.createdAtString(date: survey.updatedAt) )\n"
    }
}

enum AppointmentsCSV: String, CaseIterable {
    case id
    case title
    case status
    case kind
    case desc
    case scheduleEpoc
    case duration
    case hostLink
    case meetingLink
    case navigatorId
//    case navigator
    case memberId
    case member
    case rate
    case createdAt
    case updatedAt
        
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func row(appointment: Appointment) -> String {
        let id = appointment.id?.uuidString ?? ""
        let navId = appointment.creatorID
//        let navigator = appointment.host?.fullName() ?? ""
        let memberId = appointment.member?.id?.uuidString ?? ""
        let member = appointment.member?.fullName() ?? ""
        return "\(id),\(appointment.title),\(appointment.status),\(appointment.kind),\(appointment.desc.clean()),\(appointment.scheduleEpoc ?? 0),\(appointment.duration),\(appointment.hostLink ?? ""),\(appointment.meetingLink ?? ""),\(navId),\(memberId),\(member),\(appointment.rate ?? ""),\(Date.createdAtString(date: appointment.createdAt)),\(Date.createdAtString(date: appointment.updatedAt))\n"
    }
}

enum CarePackageItemCSV: String, CaseIterable {
    
    case networkId
    case name
    case type
    case status
    case desc
    case socialPlanId
    case socialPlan
    case socialPlanStatus
    case createdAt
    case updatedAt
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func row(item: CarePackageItem) -> String {
        let networkId = item.network?.id?.uuidString ?? "-"
        let network = item.network?.name ?? "-"
        let cpId = item.carepackage?.id?.uuidString ?? "-"
        let cpName = item.carepackage?.title ?? "-"
        let cpStatus = item.carepackage?.status ?? "-"
        return "\(networkId),\(network),\(item.type),\(item.status),\(item.desc?.clean() ?? "-"),\(cpId),\(cpName),\(cpStatus),\(Date.createdAtString(date: item.createdAt)),\(Date.createdAtString(date: item.updatedAt))\n"
    }
}

enum TaskDetailCSV: String, CaseIterable {
    case id
    case title
    case type
    case kind
    case meta
    case createdAt
    case updatedAt
        
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func row(task: TaskDetail) -> String {
        let title = csvEscape(task.title)
        let kind = csvEscape(task.kind)
        let type = csvEscape(task.type)
        let json = task.meta ?? .default
        
        return "\(task.id?.uuidString ?? ""),\(title),\(type),\(kind),\(json.asJSONString()),\(Date.createdAtString(date: task.createdAt)),\(Date.createdAtString(date: task.updatedAt))\n"
    }
}

enum TaskCSV: String, CaseIterable {
    case id
    case memberId
    case member
    case title
    case type
    case status
    case remote
    case dueAtEpoc
    case desc
    case urgent
    case completedById
    case completedBy
    case reasonTitle
    case reason
    case reasonCompletedAt
    case creatorId
    case creator
    case createdAt
    case updatedAt
    case teams
        
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func headersWithDetails() -> String {
        let detailHeaders = TaskDetailCSV.headers()
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\(detailHeaders)"
    }
    
    static func row(task: TaskModel) -> String {
        let completedbyId = task.assignee?.id?.uuidString ?? ""
        let completedby = csvEscape(task.assignee?.fullName() ?? "")
        let creatorId = task.creator?.id?.uuidString ?? ""
        let creator = csvEscape(task.creator?.fullName() ?? "")
        let teams = csvEscape(task.receivers.flatMap { $0.households }.flatMap { $0.teams }.compactMap { $0.name }.joined(separator: "-"))
        
        let reasonTitle = csvEscape(task.reason?.reason ?? "")
        let reason = csvEscape(task.reason?.msg ?? "")
        let reasonCreated =  Date.createdAtString(date: task.reason?.createdAt)
        
        let memberId = task.receivers.last?.id?.uuidString ?? ""
        let member = csvEscape(task.receivers.last?.fullName() ?? "")
        
        return "\(task.id?.uuidString ?? ""),\(memberId),\(member),\(task.title),\(task.type),\(task.status),\(task.remote),\(task.dueAtEpoc ?? 0),\(task.desc?.clean() ?? ""),\(task.urgent ?? false),\(completedbyId),\(completedby),\(reasonTitle),\(reason),\(reasonCreated),\(creatorId),\(creator),\(Date.createdAtString(date: task.createdAt)),\(Date.createdAtString(date: task.updatedAt)),\(teams)\n"
    }
    
    static func row(task: TaskModel, detail: TaskDetail) -> String {
        let detail = TaskDetailCSV.row(task: detail)
        return "\(task.id?.uuidString ?? ""),\(task.title),\(task.type),\(task.status),\(task.remote),\(task.dueAtEpoc ?? 0),\(task.desc?.clean() ?? ""),\(task.urgent ?? false),\(Date.createdAtString(date: task.createdAt)),\(Date.createdAtString(date: task.updatedAt)),\(detail)"
    }
}

//func csvEscape(_ value: String) -> String {
//    var escaped = value.replacingOccurrences(of: "\"", with: "\"\"")
//    if escaped.contains(",") || escaped.contains("\n") || escaped.contains("\"") {
//        escaped = "\"\(escaped)\""
//    }
//    return escaped
//}

func csvEscape(_ value: String) -> String {
    let cleaned = value
        .replacingOccurrences(of: "\"", with: "")          // remove quotes
        .replacingOccurrences(of: ",", with: "")           // remove commas
        .replacingOccurrences(of: "\n", with: " ")         // replace newline with space
        .replacingOccurrences(of: "\r", with: " ")         // replace carriage return with space
        .components(separatedBy: .whitespacesAndNewlines)  // split by all whitespace
        .filter { !$0.isEmpty }                            // remove empty chunks
        .joined(separator: " ")                            // join with single spaces

    let asciiCleaned = cleaned.filter { $0.isASCII }       // optional: strip non-ASCII chars
    return asciiCleaned
}

enum ChatsCSV: String, CaseIterable {
    
    case id
    case conversationFriendlyName
    case latestMessage
    case creatorId
    case creator
    case createdAt
    case updatedAt
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
        
    static func row(chat: Chat) -> String {
        let creatorID = chat.creator?.id?.uuidString ?? ""
        let creator = chat.creator?.fullName() ?? ""
        return "\(chat.id?.uuidString ?? ""),\(chat.conversationFriendlyName.clean()),\(chat.latestMessage?.clean() ?? ""),\(creatorId),\(creator),\(Date.createdAtString(date: chat.createdAt)),\(Date.createdAtString(date: chat.updatedAt))\n"
    }
}

enum ConsentCSV: String, CaseIterable {
    case id
    case memberId
    case memberName
    case name
    case type
    case url
    case createdAt
    case updatedAt
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func row(consent: Consent, memberId: String, memberName: String) -> String {
        return "\(consent.id?.uuidString ?? ""),\(memberId),\(memberName),\(consent.name),\(consent.type),\(csvEscape(consent.url)),\(Date.createdAtString(date: consent.createdAt)),\(Date.createdAtString(date: consent.updatedAt))\n"
    }
}

enum AttachmentCSV: String, CaseIterable {
    
    case id
    case memberId
    case memberName
    case name
    case kind
    case type
    case url
    case category
    case createdAt
    case updatedAt
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func row(attachment: Attachment, memberId: String, memberName: String) -> String {
        return "\(attachment.id?.uuidString ?? ""),\(memberId),\(memberName),\(attachment.name ?? ""),\(attachment.kind),\(attachment.type),\(csvEscape(attachment.url)), \(attachment.category ?? ""),\(Date.createdAtString(date: attachment.createdAt)),\(Date.createdAtString(date: attachment.updatedAt))\n"
    }
}

enum TagCSV: String, CaseIterable {
    case id = "id"
    case name = "name"
    case key = "key"
    
    static func headers(newLine:Bool = true) -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return appendNewLineFor(item: header, newLine: newLine)
    }
    
    static func row(tag: Tag, newLine: Bool = true) -> String {
        let id = tag.id?.uuidString ?? ""
        let name = csvEscape(tag.name)
        let key = csvEscape(tag.key)
        let row = "\(id),\(name),\(key)"
        return appendNewLineFor(item: row, newLine: newLine)
    }
}

enum NoteCSV: String, CaseIterable {
        
    case id
    case title
    case subtitle
    case type
    case msg
    case memberId
    case member
    case householdId
    case householdName
    case teamId
    case teamName
    case navigatorId
    case navigator
    case createdAt
    case updatedAt
    case tagId
    case tagName
    case tagkey
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }    
    
    static func row(note: Note, newLine: Bool = true) -> String {
        let household       = note.member?.households.last
        let householdId     = household?.id?.uuidString ?? ""
        let householdName   = csvEscape(household?.title ?? "")
        let team            = household?.teams.last
        let teamId          = team?.id?.uuidString ?? ""
        let teamName        = csvEscape(team?.name ?? "")
        let navigatorId     = note.creator?.id?.uuidString ?? ""
        let navigator       = csvEscape(note.creator?.fullName() ?? "")
        let subtitle        = csvEscape(note.subtitle ?? "")
        
        var tags = ""
        if let tag = note.tags.first {
            tags = TagCSV.row(tag: tag, newLine: false)
        }
//        let tags = note.tags.compactMap({ TagCSV.row(tag: $0, newLine: false)}).joined()
        
        
        let row = "\(note.id?.uuidString ?? ""),\(csvEscape(note.title.clean())),\(csvEscape(subtitle.clean())),\(note.type),\(csvEscape(note.msg.clean())), \(note.member?.id?.uuidString ?? ""),\(csvEscape(note.member?.fullName() ?? "")),\(householdId),\(householdName),\(teamId),\(teamName),\(navigatorId),\(navigator),\(Date.createdAtString(date: note.createdAt)),\(Date.createdAtString(date: note.updatedAt)),\(tags)"
        return appendNewLineFor(item: row, newLine: newLine)
    }
}

enum MemberWithAddress: String, CaseIterable {
    case id = "id"
    case email = "email"
    case first = "first"
    case middle = "middle"
    case last = "last"
    case type = "type"
    case dob = "dob"
    case gender = "gender"
    case ethnicity = "ethnicity"
    case sexualIdentity = "sexualIdentity"
    case genderIdentity = "genderIdentity"
    case pronouns = "pronouns"
    case language = "language"
    case referredBy = "referredBy"
    case lastAt = "lastAt"
    case status = "status"
    case score = "score"
    case refId = "refId"
    case pregnancyStatus = "pregnancyStatus"
    case deliveryDate = "deliveryDate"
    case military = "military"
    case enrolledOn = "enrolledOn"
    case unenrolledDate = "unenrolledDate"
    case createdAt = "createdAt"
    case updatedAt = "updatedAt"
    case addressId = "addressId"
    case street = "street"
    case street2 = "street2"
    case city = "city"
    case state = "state"
    case zip = "zip"
    case country = "country"
    case county = "county"
    case kind = "kind"
    case lat = "lat"
    case lon = "lon"
    case note = "note"
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
}

enum MemberWithAddressWithTeamWithPhoneNumber: String, CaseIterable {
    case id = "id"
    case email = "email"
    case first = "first"
    case middle = "middle"
    case last = "last"
    case type = "type"
    case dob = "dob"
    case gender = "gender"
    case ethnicity = "ethnicity"
    case sexualIdentity = "sexualIdentity"
    case genderIdentity = "genderIdentity"
    case pronouns = "pronouns"
    case language = "language"
    case referredBy = "referredBy"
    case lastAt = "lastAt"
    case status = "status"
    case score = "score"
    case refId = "refId"
    case pregnancyStatus = "pregnancyStatus"
    case deliveryDate = "deliveryDate"
    case military = "military"
    case enrolledOn = "enrolledOn"
    case unenrolledDate = "unenrolledDate"
    case createdAt = "createdAt"
    case updatedAt = "updatedAt"
    case householdId = "householdId"
    case housholdName = "housholdName"
    case teamId = "teamId"
    case teamName = "teamName"
    case addressId = "addressId"
    case street = "street"
    case street2 = "street2"
    case city = "city"
    case state = "state"
    case zip = "zip"
    case country = "country"
    case county = "county"
    case kind = "kind"
    case lat = "lat"
    case lon = "lon"
    case note = "note"
    case phone = "phone"
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
}

enum QuestionHeaders: String, CaseIterable {
    
    case id = "id"
    case title = "title"
    case message = "message"
    case value = "value"
    case score = "score"
    case sectionID = "sectionID"
    case questionID = "questionID"
    case type = "type"
    case createdAt = "createdAt"
    case updatedAt = "updatedAt"
    
    
    static func headers() -> String {
        let header = allCases.map { $0.rawValue }.joined(separator: ",")
        return "\(header)\n"
    }
    
    static func combinedHeaders() -> String {
        return """
AnswerID,TakerID,GiverID,AnswerText,AnswerValue,AnswerScore,CreatedAt,QuestionID,QuestionTitle,QuestionMessage,QuestionValue,QuestionScore,QuestionType,SectionTitle,SurveyID,SurveyTitle,SurveyKey\n
"""
    }
}

