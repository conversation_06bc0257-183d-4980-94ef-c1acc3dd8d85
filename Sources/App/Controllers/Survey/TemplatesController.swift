//
//  File.swift
//
//
//  Created by <PERSON> on 7/2/21.
//

import Foundation
import Vapor
import Fluent


let TemplateParam = "templateID"

struct TemplateInput: Content {
//    var templateID:     String
    var key:            String
    var memberID:       String
    var navigatorID:    String
    var orgID:          String
    
    func model() ->Survey {
        return Survey(name: "",
                      memberID: UUID(memberID),
                      status: "active",
                      key: "",
                      score: "",
                      startedAt: Date(),
                      endedAt: nil,
                      firstSurvey: nil,
                      lastSurvey: nil, 
                      orgID: orgID.lowercased())
    }
}

struct TemplatesController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let networks = routes.grouped("templates")
        networks.get("all", use: all)
        networks.get("key", use: key)
        networks.get(":\(TemplateParam)", use: find)
        networks.get(use: index)
        networks.post(use: createFromTemplate)
    }
    
    
    //MARK: - Fetch
    func find(req: Request) throws -> EventLoopFuture<Template> {
        return Template.find(req.parameters.get(Temp<PERSON><PERSON>ara<PERSON>), on: req.db).unwrap(or: Abort(.notFound))
    }
    
    func key(req: Request) throws -> EventLoopFuture<Template> {
        guard let type:String = req.query["type"] else { throw Abort(.notFound, reason: "key type is required") }
        return try findTemplateByKey(req: req, key: type)
    }
    
    
    func findTemplateByID(req: Request, id:String) throws -> EventLoopFuture<Template> {
        guard let networkID = UUID(id) else { throw Abort(.notFound, reason: "User ID is required")}
        return Template.find(networkID, on: req.db).flatMapThrowing() {model in
            guard let foundModel = model else { throw Abort(.notFound, reason: "User ID is required") }
            return foundModel
        }
    }
    
    static func findTemplateByKey(req: Request, key:String) throws -> EventLoopFuture<Template> {
        return Template.query(on: req.db).filter(\.$key == key.lowercased()).first().flatMapThrowing { template in
            guard let temp = template else { throw Abort(.notFound, reason: "Template with \(key) does not exist.") }
            return temp
        }
    }
    
    func findTemplateByKey(req: Request, key:String) throws -> EventLoopFuture<Template> {
        return Template.query(on: req.db).filter(\.$key == key.lowercased()).first().flatMapThrowing { template in
            guard let temp = template else { throw Abort(.notFound, reason: "Template with \(key) does not exist.") }
            return temp
        }
    }
        
    func all(req: Request) throws -> EventLoopFuture<[Template]> {
        let org:String?            = req.query["org"]
        let kind: String?          = req.query["kind"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        let query = Template.query(on: req.db).filter(\.$orgID == orgID)
        if let knd = kind {
            query.filter(\.$kind == knd)
        }
        return query.all()
    }
    
    func index(req: Request) throws -> EventLoopFuture<[TemplateSelection]> {
        let org:String?            = req.query["org"]
        let kind: String?          = req.query["kind"]
        guard let orgID = org else { throw NetworkError.error(type: .organization) }
        let query = Template.query(on: req.db).filter(\.$orgID == orgID)
        if let knd = kind {
            query.filter(\.$kind == knd)
        }
        
        return query.all().map {
                $0.map { TemplateSelection(title: $0.name, key: $0.key)}
            }
            
    }
    
    static func extractTempate(req:Request, text:String) throws -> EventLoopFuture<(title:String, value:[String: Any])> {
        guard let json = convertToDictionary(text: text) else { throw NetworkError.error(type: .badRequest, msg: "Could not create survey from template.") }
        guard let name = json["name"] as? String else { throw NetworkError.error(type: .badRequest, msg: "Template missing name.") }
        return req.eventLoop.future((name, json))
    }
    
    fileprivate func extractTempate(req:Request, text:String) throws -> EventLoopFuture<(title:String, value:[String: Any])> {
        guard let json = convertToDictionary(text: text) else { throw NetworkError.error(type: .badRequest, msg: "Could not create survey from template.") }
        guard let name = json["name"] as? String else { throw NetworkError.error(type: .badRequest, msg: "Template missing name.") }
        return req.eventLoop.future((name, json))
    }
    
    //MARK: - Create
    func createFromTemplate(req: Request) throws -> EventLoopFuture<Survey> {
        let input = try req.content.decode(TemplateInput.self)
        let survey = input.model()
        return try UsersController.find(req: req, id: input.navigatorID).flatMap { navigator in
            //            try! self.findTemplateByID(req: req, id: input.templateID).flatMap
            return try! self.findTemplateByKey(req: req, key: input.key).flatMap { temp in
                if temp.kind == "assessment"  {
                    return try! extractTempate(req: req, text: temp.template.template).flatMap { data in
                        survey.name = data.title
                        survey.key = temp.key
                        survey.$taker.id = navigator.id
                        return survey.create(on: req.db).transform(to: survey).flatMap { newSurvey in
                            return try! self.buildTemplate(req: req, survey: newSurvey, json: data.value).flatMap { _ in
                                if let id = newSurvey.id?.uuidString {
                                    return try! SurveysController.findSurvey(req: req, id: id)
                                } else {
                                    return req.eventLoop.future(newSurvey)
                                }
                                
                            }
                        }
                    }
                } else {
                    return req.eventLoop.makeFailedFuture(NetworkError.error(type: .badRequest,
                                                                             msg: "Templates must be of kind assessment.") )
                }
            }
        }
    }
            
    
    func buildTemplate(req: Request, survey:Survey, json: [String:Any]) throws -> EventLoopFuture<Void> {
        let sections = json["sections"] as? [[String:Any]] ?? []
        let map = sections.reduce(into: [String: [[String:Any]]]()) { (hash, json) in
            guard let questions = json["questions"] as? [[String:Any]] else { return }
            guard let title     = json["title"] as?  String else { return }
            hash[title] = questions
        }
        
        let sectionsToBeSaved = try sections.compactMap({ try self.createSection(req: req, json: $0, survey: survey)})
                
        return survey.$sections.create(sectionsToBeSaved, on: req.db).transform(to: sectionsToBeSaved).flatMap { newSections  in
            return newSections.sequencedFlatMapEach(on: req.eventLoop) { data in
                guard let questions = map[data.title] else { return req.eventLoop.future() }
                return questions.sequencedFlatMapEach(on: req.eventLoop) { json in
                    return try! self.createParentQuestion(req: req, json: json, section: data)
                }
            }
        }
    }
    
    fileprivate func createSection(req:Request, json:[String:Any], survey:Survey) throws -> Section {
        guard let surveyUUID = survey.id                    else { throw Abort(.notFound, reason: "Survey ID is required") }
        guard let title      = json["title"]     as? String else { throw Abort(.notFound, reason: "Title is required")     }
        guard let complete   = json["complete"]  as? Bool   else { throw Abort(.notFound, reason: "Complete is required")  }
              let type       = json["type"]      as? String
        print(title)
        return Section(title: title, score:nil, complete: complete, surveyID: surveyUUID, type: type)
    }
    
    fileprivate func createParentQuestion(req:Request, json:[String:Any], section:Section?) throws -> EventLoopFuture<Void> {
        
        guard let sectionID = section?.id else                { return      req.eventLoop.future() }
        guard let message   = json["message"] as? String else { return      req.eventLoop.future() }
        guard let level     = json["level"]   as? Int else    { return      req.eventLoop.future() }
        guard let value     = json["value"]   as? Int else    { return      req.eventLoop.future() }
        let score = json["score"] as? Int
        let title = json["title"] as? String
        let subquestions = json["questions"] as? [[String:Any]] ?? []
        let serviceType:ServiceType? = ServiceType(rawValue: json["type"] as? String ?? "")
        print("\(title ?? ""):\(level)")
        let question = Question(title: title, message: message, level: level, value: value, score: score, sectionID: sectionID, questionID: nil, type: serviceType)
        
        return Section.find(sectionID, on: req.db).flatMap({ data in
            return data!.$questions.create(question, on: req.db).transform(to: question).flatMap { ques in
                
                return subquestions.sequencedFlatMapEach(on: req.eventLoop) { jsonQues in
                    guard let questionToCreate = self.createSubQuestion(req: req, json: jsonQues, question: ques) else { return req.eventLoop.future() }
//                    print("questionToCreate Level:\(questionToCreate.level)")
                    
                    let subQuestions = jsonQues["questions"] as? [[String:Any]] ?? []
                    
                    return ques.$questions.create(questionToCreate, on: req.db).transform(to: questionToCreate).flatMap { subsubsubQuestion in
                        
                        return subQuestions.sequencedFlatMapEach(on: req.eventLoop) { subsubJSON in
                            
                            guard let questionToCreate2 = self.createSubQuestion(req: req, json: subsubJSON, question: subsubsubQuestion) else {
                                return req.eventLoop.future()
                            }
                            return subsubsubQuestion.$questions.create(questionToCreate2, on: req.db)
                        }
                    }
                }
            }
        })
    }
    
   fileprivate func createSubQuestion(req:Request, json:[String:Any], question:Question?) ->  Question? {
        guard let message   = json["message"] as? String else { return  nil}
        guard let level     = json["level"]   as? Int else    { return  nil}
        guard let value     = json["value"]   as? Int else    { return  nil}
        
        let score = json["score"] as? Int
        let title = json["title"] as? String
        let serviceType:ServiceType? = ServiceType(rawValue: json["type"] as? String ?? "")
        return Question(title: title, message: message, level: level, value: value, score: score, sectionID: nil, questionID: nil, type:serviceType)
    }
}


func convertToDictionary(text: String) -> [String: Any]? {
    if let data = text.data(using: .utf8) {
        do {
            return try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
        } catch {
//            print(error.localizedDescription)
        }
    }
    return nil
}

