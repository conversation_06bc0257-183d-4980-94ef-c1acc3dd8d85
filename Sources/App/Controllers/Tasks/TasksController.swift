//
//  File.swift
//  
//
//  Created by <PERSON> on 11/30/23.
//

import Foundation
import Vapor
import Fluent

struct TasksCompleteInput: Content {
    var orgID: String
    var status: String
    var reason: ReasonInput
    var duration: String?
}

struct ReasonInput: Content {
    var reason: String
    var msg:    String
    var creatorID: String
    let createNote: Bool?
    
    func model() -> Reason {
        return Reason(reason: reason, msg: msg)
    }
}

struct TasksResponse: Content {
    var tasks: [String:[TaskModel]]
    var metadata: PageMetadata? = nil
}

struct TasksArchiveInput: Content {
    var userId: String
}

struct TasksUpdateInput: Content {
    var title: String?
    var type: String?
    var status: String?
    var remote: Bool?
    var dueAtEpoc: Int?
    var desc: String?
    var meta: MetaData?
    var orgID: String?
    var creatorID: String?
    var assigneeID: String?
    var location: AddressInput?
    var taskDetail: TaskDetailInput?
    var completedBy: [String]? //userIDS
    var receivers: [String]? //memberIDS
    var attachments: [UploadInput]?
    var urgent: Bool?
    var duration: String?
     
    func returnModel() -> TasksUpdateInput {
        var input = self
        input.title = title?.lowercased()
        input.type = type?.lowercased()
        input.status = status?.lowercased()
        input.desc = desc?.lowercased()
        input.orgID = orgID?.lowercased()
        input.creatorID = creatorID?.lowercased()
        input.assigneeID = assigneeID?.lowercased()
        input.urgent = urgent
        return input
    }
    
    func returnUpdatedModel(task:TaskModel) -> TaskModel {
        if let title = title {
            task.title = title
        }
        if let type = type {
            task.type = type
        }
        if let status = status {
            task.status = status
        }
        if let remote = remote {
            task.remote = remote
        }
        if let dueAtEpoc = dueAtEpoc {
            task.dueAtEpoc = dueAtEpoc
        }
        if let desc = desc {
            task.desc = desc
        }
        if let meta = meta {
            task.meta = meta
        }
        if let urgent = urgent {
            task.urgent = urgent
        }
        
        return task
    }
    
    func returnUpdatedModel(taskDetail:TaskDetail) -> TaskDetail {
        
        if let title = self.taskDetail?.title?.lowercased() {
            taskDetail.title = title
        }
        
        if let type = self.taskDetail?.type?.lowercased() {
            taskDetail.type = type
        }
        
        if let kind = self.taskDetail?.kind?.lowercased() {
            taskDetail.kind = kind
        }
        
        if let meta = self.taskDetail?.meta {
            taskDetail.meta = meta
        }
        
        return taskDetail
    }
    
    func detailModel() -> TaskDetail {
        return TaskDetail(title: self.taskDetail?.title?.lowercased() ?? "",
                          type: self.taskDetail?.type?.lowercased() ?? "",
                          kind: self.taskDetail?.kind?.lowercased() ?? "",
                          meta: self.taskDetail?.meta)
    }
    
    func isArchived() -> Bool {
        return status?.lowercased().contains("archive") ?? false
    }
}

struct TasksCreateInput: Content {
    var title: String
    var type: String
    var status: String
    var remote: Bool
    var dueAtEpoc: Int
    var desc: String
    var meta: MetaData?
    var orgID: String?
    var creatorID: String?
    var assigneeID: String?
    var location: AddressInput?
    var taskDetail: TaskDetailInput
    var completedBy: [String]? //userIDS
    var receivers: [String]? //memberIDS
    var urgent: Bool?
    var meetinglink: String?
    var attachments: [UploadInput]?
    
     
    func cleanUp() -> TasksCreateInput {
        var input = self
        input.title = title.lowercased()
        input.type = type.lowercased()
        input.status = status.lowercased()
        input.desc = desc.lowercased()
        input.orgID = orgID?.lowercased()
        input.creatorID = creatorID?.lowercased()
        input.assigneeID = assigneeID?.lowercased()
        input.urgent = urgent
        return input
    }
    
    func taskModel() -> TaskModel {
        let input = cleanUp()
        return TaskModel(title: input.title,
                         type: input.type,
                         status: input.status,
                         remote: input.remote,
                         dueAtEpoc: input.dueAtEpoc,
                         desc: input.desc,
                         meta: input.meta,
                         urgent: input.urgent)
    }
    
    
    func detailModel() -> TaskDetail {
        return TaskDetail(title: self.taskDetail.title?.lowercased() ?? "",
                          type: self.taskDetail.type?.lowercased() ?? "",
                          kind: self.taskDetail.kind?.lowercased() ?? "",
                          meta: self.taskDetail.meta)
    }
}

struct EligibilityInput: Content {
    var insurance: String
    var orgId:String
}


struct TaskAssessmentInput: Content {
    var template: TemplateInput
    var taskId: String
}

struct TaskFilter: Content {
    var orgID: String
    var types: [String]?
    var completedBy: [String]? //users
    var receivers: [String]? //members
    var assignees: [String]? //users
    var status: [String]? //status
    // Add pagination parameters
    var page: Int?
    var perPage: Int?
}

// Status pagination metadata
struct StatusPaginationMetadata: Content {
    let page: Int
    let perPage: Int
    let total: Int
    let totalPages: Int
}

// Updated response struct with status-specific metadata
struct TasksPageResponse: Content {
    let tasks: [String: [TaskModel]]
    let metadata: [String: StatusPaginationMetadata]?
}

struct TasksController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let tasks = routes.grouped("tasks")
        tasks.get(use: index)
        tasks.get("page", use: indexPage)
        tasks.get(["org", ":org","status"], use: findOrgTaskStatus)
        tasks.get(":taskID", use: fetchTask)
        tasks.get("date", use: tasksByDate)
        
        tasks.post("filter", use: filterTasks)
        tasks.post("eligibility", use: eligibility)
        tasks.post([":taskID", "survey"], use: createSurvey)
        tasks.post(use: create)
        tasks.delete(":taskID", use: delete)
        tasks.put("archive", use: archive)
        tasks.put(":taskID", use: update)
        tasks.put([":taskID", "drop"], use: drop)
        tasks.put([":taskID", "complete"], use: complete)
        tasks.post([":taskID"], use: addAttachment)
    }
    
    //MARK - Fetch
    func eligibility(req: Request) throws -> EventLoopFuture<[TemplateSelection]> {
        let input = try req.content.decode(EligibilityInput.self)
        return Template.query(on: req.db)
            .filter(\.$orgID == input.orgId)
            .filter(\.$kind == "assessment")
            .all().map {
                $0.map { TemplateSelection(title: $0.name, key: $0.key)}
            }
    }
    
    func filterTasks(req: Request) throws -> EventLoopFuture<TasksResponse> {
        let input = try req.content.decode(TaskFilter.self)
        return try self.fetchOrgStatus(req: req, id: input.orgID).flatMap { orgTaskStatus in
            var json: [String:[TaskModel]] = [:]
            return orgTaskStatus.status.sequencedFlatMapEach(on: req.eventLoop) { status in
                json[status] = []
                return req.eventLoop.future()
            }
            .flatMap({ _ in
                let query = TaskModel.query(on: req.db)
                    .with(\.$org)
                    .with(\.$attachments)
                    .with(\.$assessments)
                    .with(\.$receivers, { member in
                        member.with(\.$attachments)
                        member.with(\.$phones)
                    })
                    .with(\.$completedBy, { user in
                        user.with(\.$attachments)
                        user.with(\.$teams)
                    })
                    .with(\.$taskDetail)
                    .with(\.$creator)
                    .with(\.$location)
                    .with(\.$assignee)
                    .with(\.$reason, { rsn in })
                
                
                if let types = input.types, !types.isEmpty {
                    query.filter(\.$type ~~ types)
                }
                
                if let status = input.status, !status.isEmpty {
                    query.filter(\.$status ~~ status)
                }
                
                if let completedBy = input.completedBy, !completedBy.isEmpty {
                    query
                        .join(siblings: \.$completedBy)
                        .filter(User.self, \.$id ~~ completedBy.map({UUID(uuidString: $0)!}))
                }
                
                if let receivers = input.receivers, !receivers.isEmpty {
                    query
                        .join(siblings: \.$receivers)
                        .filter(Member.self, \.$id ~~ receivers.map({UUID(uuidString: $0)!}))
                        
                }
                
                if let assignees = input.assignees, !assignees.isEmpty {
                    query
                        .join(parent: \.$assignee)
                        .filter(User.self, \.$id ~~ assignees.map({UUID(uuidString: $0)!}))
                }
                    
                return query
                    .sort(\.$dueAtEpoc, .ascending)
                    .all().flatMap { models in
                        do {
                            return try mapTasks(req: req, items: models, json: json).flatMap { json in
                                return req.eventLoop.future(TasksResponse(tasks: json, metadata: nil))
                            }
                        } catch {
                            return req.eventLoop.future(TasksResponse(tasks: json, metadata: nil))
                        }
                    }
            })
        }
    }
    
    fileprivate func mapTasks(req: Request, items: [TaskModel], json: [String:[TaskModel]]) throws -> EventLoopFuture<[String:[TaskModel]]> {
        var json = json
        return items.sequencedFlatMapEach(on: req.eventLoop) { item in
            if var dataArray = json[item.status] {
                dataArray.append(item)
                json[item.status] = dataArray
            } else {
                json[item.status] = [item]
            }
            return req.eventLoop.future()
        }
        .flatMap({ _ in
            return req.eventLoop.future(json)
        })
    }

    
    func createSurvey(req: Request) throws -> EventLoopFuture<Survey> {
        let input = try req.content.decode(TaskAssessmentInput.self)
        let templateInput = input.template
        let survey = templateInput.model()
        return try! TasksController.find(req: req, id: input.taskId).flatMap { task in
            return try! UsersController.find(req: req, id: templateInput.navigatorID).flatMap { navigator in
                return try! TemplatesController.findTemplateByKey(req: req, key: templateInput.key).flatMap { temp in
                    return try! TemplatesController.extractTempate(req: req, text: temp.template.template).flatMap { data in
                        survey.name = data.title
                        survey.key = temp.key
                        survey.$taker.id = navigator.id
                        return task.$assessments.create(survey, on: req.db).transform(to: survey).flatMap { newSurvey in
                            return try! TemplatesController().buildTemplate(req: req, survey: newSurvey, json: data.value).flatMap { _ in
                                if let id = newSurvey.id?.uuidString {
                                    return try! SurveysController.findSurvey(req: req, id: id)
                                } else {
                                    return req.eventLoop.future(newSurvey)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    
    func fetchTask(req: Request) throws -> EventLoopFuture<TaskModel> {
        guard let task:String = req.parameters.get("taskID") else { throw NetworkError.error(type: .badRequest, msg: "task id is required.") }
        return TaskModel.query(on: req.db)
            .with(\.$org)
            .with(\.$attachments)
            .with(\.$assessments)
            .with(\.$receivers, { member in
                member.with(\.$attachments)
                member.with(\.$phones)
                member.with(\.$households)
            })
            .with(\.$completedBy, { user in
                user.with(\.$attachments)
                user.with(\.$teams)
            })
            .with(\.$taskDetail)
            .with(\.$creator)
            .with(\.$location)
            .with(\.$assignee)
            .with(\.$reason, { rsn in })
            .filter(\.$id == UUID(uuidString: task)!).first().unwrap(or: Abort(.notFound))
    }
    
    func tasksByDate(req: Request) throws -> EventLoopFuture<[TaskModel]> {
        guard let org:String = req.query["org"] else { throw NetworkError.error(type: .badRequest, msg: "Org is required.") }
        guard let timestamp:Int = req.query["dueAtEpoc"] else { throw NetworkError.error(type: .badRequest, msg: "dueAtEpoc is requried.") }
        let id:String? = req.query["navigator"]
        let (start, end) = getDayBoundaries(fromEpochTimestamp: TimeInterval(timestamp))
        let query = TaskModel.query(on: req.db)
            .join(parent: \.$org)
            .filter(Organization.self, \.$id == UUID(org)!)
            .with(\.$org)
            .with(\.$attachments)
            .with(\.$assessments)
            .filter(\.$dueAtEpoc >= start)
            .filter(\.$dueAtEpoc <= end)
            .with(\.$receivers, { member in
                member.with(\.$attachments)
                member.with(\.$phones)
            })
            .with(\.$completedBy, { user in
                user.with(\.$attachments)
                user.with(\.$teams)
            })
            .with(\.$taskDetail)
            .with(\.$creator)
            .with(\.$location)
            .with(\.$assignee, { user in
                user.with(\.$teams)
            })
            .with(\.$reason, { rsn in })
            .sort(\.$dueAtEpoc, .ascending)
        
        if let navID = id?.lowercased() {
            query.join(siblings: \.$completedBy)
                .filter(User.self, \.$id ~~ [UUID(uuidString: navID)!])
        }
        
        return query.all()
    }
    func getDayBoundaries(fromEpochTimestamp timestamp: TimeInterval) -> (start: Int, end: Int) {
        // Convert epoch timestamp (seconds since Jan 1, 1970 UTC) to a Date object
        let date = Date(timeIntervalSince1970: timestamp)
        
        // Use a UTC-configured calendar
        var calendar = Calendar.current
        calendar.timeZone = TimeZone(identifier: "UTC")! // Force UTC timezone
        
        // Start of the day (midnight, 00:00:00 UTC)
        let startOfDay = calendar.startOfDay(for: date)
        
        // End of the day (23:59:59 UTC)
        guard let endOfDay = calendar.date(bySettingHour: 23, minute: 59, second: 59, of: startOfDay) else {
            fatalError("Failed to calculate end of day")
        }
        
        return (start: Int(startOfDay.timeIntervalSince1970), end: Int(endOfDay.timeIntervalSince1970))
    }
    
    func startAndEndOfDay(for epoch: TimeInterval) -> (startOfDay: TimeInterval, endOfDay: TimeInterval) {
        let date = Date(timeIntervalSince1970: epoch)
        let calendar = Calendar.current

        let startOfDay = calendar.startOfDay(for: date).timeIntervalSince1970
        let endOfDay = startOfDay + 86_399 // Add 86,399 seconds (23h 59m 59s)

        return (startOfDay, endOfDay)
    }

    func indexPage(req: Request) throws -> EventLoopFuture<TasksPageResponse> {
        guard let org: String = req.query["org"] else { throw NetworkError.error(type: .badRequest, msg: "Org is required.") }
        let id: String? = req.query["navigator"]
        let search: String? = req.query["search"]
        
        // Get pagination parameters
        let page = req.query[Int.self, at: "page"] ?? 1
        let perPage = req.query[Int.self, at: "perPage"] ?? 10
        
        // Hardcoded status values
        let statusValues = ["pending", "picked up", "completed"]
        
        // Create futures array to store all status bucket queries
        var statusFutures: [EventLoopFuture<(String, [TaskModel], Int)>] = []
        
        // For each status, create a separate query with its own pagination
        for status in statusValues {
            // Create base query builder that will be used for both count and fetch
            func createBaseQuery() -> QueryBuilder<TaskModel> {
                let query = TaskModel.query(on: req.db)
                    .join(parent: \.$org)
                    .filter(Organization.self, \.$id == UUID(org)!)
                    .filter(\.$status == status)
                
                if let navID = id?.lowercased() {
                    query.join(siblings: \.$completedBy)
                        .filter(User.self, \.$id ~~ [UUID(uuidString: navID)!])
                }
                
                if let search = search {
                    query.filter(\.$title, .custom("ilike"), "%\(search.lowercased())%")
                }
                
                return query
            }
            
            // First count total tasks in this status
            let countFuture = createBaseQuery().count()
            
            // Then fetch the paginated tasks for this status
            let tasksFuture = countFuture.flatMap { totalCount in
                let query = createBaseQuery()
                    .with(\.$org)
                    .with(\.$attachments)
                    .with(\.$assessments)
                    .with(\.$receivers, { member in
                        member.with(\.$attachments)
                        member.with(\.$phones)
                    })
                    .with(\.$completedBy, { user in
                        user.with(\.$attachments)
                        user.with(\.$teams)
                    })
                    .with(\.$taskDetail)
                    .with(\.$creator)
                    .with(\.$location)
                    .with(\.$assignee, { user in
                        user.with(\.$teams)
                    })
                    .with(\.$reason, { rsn in })
                    .sort(\.$dueAtEpoc, .ascending)
                    .offset((page - 1) * perPage)
                    .limit(perPage)
                
                return query.all().map { models in
                    // Return tuple of (status, tasks, totalCount)
                    return (status, models, totalCount)
                }
            }
            
            statusFutures.append(tasksFuture)
        }
        
        // Initialize empty task dictionary
        var json: [String: [TaskModel]] = [:]
        for status in statusValues {
            json[status] = []
        }
        
        // When all status queries complete, combine results
        return EventLoopFuture.whenAllSucceed(statusFutures, on: req.eventLoop).map { results in
            var metadataDict: [String: StatusPaginationMetadata] = [:]
            
            for (status, tasks, totalCount) in results {
                json[status] = tasks
                
                // Calculate pagination metadata for this status
                let totalPages = Int(ceil(Double(totalCount) / Double(perPage)))
                metadataDict[status] = StatusPaginationMetadata(
                    page: page,
                    perPage: perPage,
                    total: totalCount,
                    totalPages: totalPages
                )
            }
            
            return TasksPageResponse(tasks: json, metadata: metadataDict)
        }
    }
    
    //OLD api
    func index(req: Request) throws -> EventLoopFuture<TasksResponse> {
        guard let org:String = req.query["org"] else { throw NetworkError.error(type: .badRequest, msg: "Org is required.") }
        let id:String? = req.query["navigator"]
        let search:String? = req.query["search"]
        return try self.fetchOrgStatus(req: req, id: org).flatMap { orgTaskStatus in
            
            var json: [String:[TaskModel]] = [:]
            return orgTaskStatus.status.sequencedFlatMapEach(on: req.eventLoop) { status in
                json[status] = []
                return req.eventLoop.future()
            }
            .flatMap({ _ in
                let query = TaskModel.query(on: req.db)
                    .join(parent: \.$org)
                    .filter(Organization.self, \.$id == UUID(org)!)
                    .with(\.$org)
                    .with(\.$attachments)
                    .with(\.$assessments)
                    .with(\.$receivers, { member in
                        member.with(\.$attachments)
                        member.with(\.$phones)
                    })
                    .with(\.$completedBy, { user in
                        user.with(\.$attachments)
                        user.with(\.$teams)
                    })
                    .with(\.$taskDetail)
                    .with(\.$creator)
                    .with(\.$location)
                    .with(\.$assignee, { user in
                        user.with(\.$teams)
                    })
                    .with(\.$reason, { rsn in })
                    .sort(\.$dueAtEpoc, .ascending)
                
                if let navID = id?.lowercased() {
                    query.join(siblings: \.$completedBy)
                        .filter(User.self, \.$id ~~ [UUID(uuidString: navID)!])
                }
                
                if let search = search  {
                    query.filter(\.$title, .custom("ilike"), "%\(search.lowercased())%")
                }
                
                return query
                    .all()
                    .flatMap { models in
                        return try! mapTasks(req: req, items: models, json: json).flatMap { json in
                            return req.eventLoop.future(TasksResponse(tasks: json, metadata: nil))
                        }
                    }
            })
        }
    }
    
    func findOrgTaskStatus(req: Request) throws -> EventLoopFuture<OrgTaskStatus> {
        guard let orgString = req.parameters.get("org") else { throw NetworkError.error(type: .badRequest, msg: "Org is required.") }
        return try fetchOrgStatus(req: req, id: orgString)
    }
    
    fileprivate func fetchOrgStatus(req: Request, id: String) throws -> EventLoopFuture<OrgTaskStatus> {
        guard let org:UUID = UUID(uuidString: id) else {
            throw Abort(.notFound, reason: "org id missing")
    }
        return OrgTaskStatus.query(on: req.db).filter(\.$orgID == org).first().unwrap(or:  Abort(.notFound))
    }
    
//    fileprivate func mapTasks(req: Request, items: [TaskModel], json: [String:[TaskModel]]) throws -> EventLoopFuture<[String:[TaskModel]]> {
//        var json = json
//        return items.sequencedFlatMapEach(on: req.eventLoop) { item in
//            if var dataArray = json[item.status] {
//                dataArray.append(item)
//                json[item.status] = dataArray
//            } else {
//                json[item.status] = [item]
//            }
//            return req.eventLoop.future()
//        }
//        .flatMap({ _ in
//            return req.eventLoop.future(json)
//        })
//    }

    static func find(req: Request, id: String) throws -> EventLoopFuture<TaskModel> {
        return TaskModel.query(on: req.db)
            .filter(\.$id == UUID(uuidString: id)!)
            .with(\.$org)
            .with(\.$attachments)
            .with(\.$assessments)
            .with(\.$receivers, { member in
                member.with(\.$attachments)
            })
            .with(\.$completedBy)
            .with(\.$taskDetail)
            .with(\.$creator)
            .with(\.$location)
            .with(\.$assignee)
            .with(\.$reason)
            .first().unwrap(or:  Abort(.notFound))
    }
    
    //MARK: - Create
    func create(req: Request) throws -> EventLoopFuture<TaskModel> {
        var input = try req.content.decode(TasksCreateInput.self)
        input = input.cleanUp()
        let memberIDs = input.receivers?.compactMap({$0.lowercased()}) ?? []
        let navigatorsIDs = input.completedBy?.compactMap({$0.lowercased()}) ?? []
        
        guard !memberIDs.isEmpty else { throw NetworkError.error(type: .badRequest, msg: "members are required")}
        guard !navigatorsIDs.isEmpty else { throw NetworkError.error(type: .badRequest, msg: "navigators are required")}
        guard let creatorID = input.creatorID else { throw NetworkError.error(type: .badRequest, msg: "creators are required")}
        guard let uuidCreator = UUID(uuidString: creatorID) else { throw NetworkError.error(type: .badRequest, msg: "creators are required")}
        
        return try! OrgsController.find(req: req, id: input.orgID).flatMap { org in
            
            return try! fetchOrgStatus(req: req, id: input.orgID ?? "").flatMap { orgStatus in
                
                let taskModel = applyIntial(orgTaskStatus: orgStatus,
                                            ifNeededFor: input.taskModel())
                
                return org.$tasks.create(taskModel, on: req.db).transform(to: taskModel).flatMap { task in
                    
                    let detail =  input.detailModel()
                    
                    return try! TasksController.find(req: req, id: task.id?.uuidString ?? "").flatMap { updatedTask in
                        
                        return updatedTask.$taskDetail.create(detail, on: req.db).transform(to: updatedTask).flatMap { taskMod in
                            
                            task.$creator.id = uuidCreator
                            
                            return task.update(on: req.db).transform(to: taskModel).flatMap { model in
                                
                                return try! createAssigneeIfNeeded(req: req, assigneeID: input.assigneeID, task: model).flatMap({ model in
                                  
                                    return try! createAddressIfNeeded(req: req, input: input.location, task: taskMod).flatMap({ model in
                                        
                                        return try! MembersController.findMembers(req: req, ids: memberIDs).flatMap({ allMembers in
                                            
                                            return try! attachMembersTo(req: req, task: taskModel, members: allMembers).flatMap { model in
                                                
                                                return try! UsersController.findAll(req: req, ids: navigatorsIDs).flatMap({ allNavs in
                                                    
                                                    return try! attachNavigatorsTo(req: req, task: task, navigators: allNavs).flatMap { model in
                                                        
                                                        return try! createAttachments(req: req, task: task, inputs: input.attachments).flatMap({ model in
                                                            
                                                            return try! TasksController.find(req: req, id: task.id?.uuidString ?? "").flatMap { updatedTask in
                                                                
                                                                return try! createAppointmentIfNeeded(req: req, task: updatedTask, link: input.meetinglink).flatMap { savedTask in
                                                                    
                                                                    return try! sendCreateTaskPush(req: req, task: updatedTask, creatorID: uuidCreator).transform(to: savedTask)
                                                                    
                                                                }
                                                            }
                                                        })
                                                    }
                                                })
                                            }
                                        })
                                    })
                                })
                            }
                        }
                    }
                }
                
            }
        }
    }
    
    fileprivate func applyIntial(orgTaskStatus:OrgTaskStatus, ifNeededFor task: TaskModel) -> TaskModel {
        if task.status.isEmpty {
            task.status =  orgTaskStatus.intialStatus
            return task
        } else {
            return task
        }
    }
    
    //MARK: - Update
    func drop(req: Request) throws -> EventLoopFuture<TaskModel> {
        guard let id = req.parameters.get("taskID") else { throw NetworkError.error(type: .badRequest, msg: "Task id is required.") }
        let input = try req.content.decode(TasksUpdateInput.self)

        return try TasksController.find(req: req, id: id).flatMap { task in
            
            let taskModel = input.returnUpdatedModel(task: task)
            
            return taskModel.update(on: req.db).transform(to: task)
        }
    }
    
    fileprivate func createTaskNote(req: Request, input: TasksCompleteInput, task: TaskModel) throws -> EventLoopFuture<Void> {
        let model = NoteInput(creator: input.reason.creatorID,
                              title: task.title,
                              type: "basic_note",
                              msg: input.reason.msg).note()
        return model.create(on: req.db).transform(to: model).flatMap { note in
            let reasonKey = input.reason.reason
            let name = reasonKey.replacingOccurrences(of: "_", with: " ").capitalized
            note.$creator.id = UUID(uuidString: input.reason.creatorID)
            note.$member.id = UUID(uuidString: task.receivers.last?.id?.uuidString ?? "")
            return note.update(on: req.db).flatMap { _ in
                return note.$tags.create([
                    .init(name: "Task Note", key: "task_note", color: "#E9CEF2"),
                    .init(name: name, key: reasonKey, color: "#A754C3")
                ], on: req.db)
            }
        }
    }
    
    func complete(req: Request) throws -> EventLoopFuture<TaskModel> {
        guard let id = req.parameters.get("taskID") else { throw NetworkError.error(type: .badRequest, msg: "Task id is required.") }
        let input = try req.content.decode(TasksCompleteInput.self)
        guard input.status.lowercased() == "completed" else { throw NetworkError.error(type: .badRequest, msg: "reason must be completed.") }
        let createNote = input.reason.createNote ?? false
        let reason = input.reason.model()
        reason.$creator.id = UUID(uuidString: input.reason.creatorID)
        return try TasksController.find(req: req, id: id).flatMap { task in
            
            return reason.create(on: req.db).transform(to: reason).flatMap { reason in
                
                task.status = input.status
                task.$reason.id = reason.id
                
                if createNote {
                    return try! createTaskNote(req: req, input: input, task: task).flatMap { _ in
                        return task.update(on: req.db).transform(to: task).flatMap { savedTask in
                            if task.isAppointmentTypeTask(), let duration = input.duration {
                                return try! AppointmentsController.findTaskAppointment(req: req, uuid: task.id).flatMap { appointment in
                                    if let apt = appointment {
                                        
                                        //status driven off of constants dropdown. taskCompletionReasons
                                        let status = input.reason.reason.lowercased() == "incomplete" ? "incomplete" : "complete"
                                        apt.duration = duration
                                        apt.status = status
                                        return apt.update(on: req.db).transform(to: task)
                                    } else {
                                        return req.eventLoop.future(task)
                                    }
                                }
                            } else {
                                return req.eventLoop.future(task)
                            }
                        }
                    }
                } else {
                    return task.update(on: req.db).transform(to: task).flatMap { savedTask in
                        if task.isAppointmentTypeTask(), let duration = input.duration {
                            return try! AppointmentsController.findTaskAppointment(req: req, uuid: task.id).flatMap { appointment in
                                if let apt = appointment {
                                    
                                    //status driven off of constants dropdown. taskCompletionReasons
                                    let status = input.reason.reason.lowercased() == "incomplete" ? "incomplete" : "complete"
                                    apt.duration = duration
                                    apt.status = status
                                    return apt.update(on: req.db).transform(to: task)
                                } else {
                                    return req.eventLoop.future(task)
                                }
                            }
                        } else {
                            return req.eventLoop.future(task)
                        }
                    }
                }
            }
        }
    }
    
    fileprivate func updateTaskDetailIfNeeded(req: Request, task: TaskModel, input: TasksUpdateInput) throws -> EventLoopFuture<TaskModel> {
        if let detail = task.taskDetail {
            let taskModel = input.returnUpdatedModel(taskDetail: detail)
            return taskModel.update(on: req.db).transform(to: task)
        } else {
            return req.eventLoop.future(task)
        }
    }
    
    func archive(req: Request) throws -> EventLoopFuture<String> {
        let input = try req.content.decode(TasksArchiveInput.self)
        return TaskModel.query(on: req.db)
            .filter(\.$assignee.$id == UUID(uuidString: input.userId))
            .filter(\.$status == "completed")  // Filter where status is "completed"
            .set(\.$status, to: "archive")    // Set status to "archive"
            .update()
            .map { count in
                return "Updated \(count) tasks to 'archive'"
            }
    }
    
    func update(req: Request) throws -> EventLoopFuture<TaskModel> {
        guard let id = req.parameters.get("taskID") else { throw NetworkError.error(type: .badRequest, msg: "Task id is required.") }
        let input = try req.content.decode(TasksUpdateInput.self)
        let navigatorsIDs = input.completedBy ?? []
        let memberIDs = input.receivers ?? []
        return try TasksController.find(req: req, id: id).flatMap { task in
                        
            let taskModel = input.returnUpdatedModel(task: task)
            
            return taskModel.update(on: req.db).transform(to: task).flatMap { model in
                
                return try! updateTaskDetailIfNeeded(req: req, task: task, input: input).flatMap { taskMod in
                    
                    return try! createAssigneeIfNeeded(req: req, assigneeID: input.assigneeID, task: taskModel).flatMap({ model in
                      
                        return try! createAddressIfNeeded(req: req, input: input.location, task: task).flatMap({ task in
                                
                            //handle empty case
                            return try! attachMembersIfNeeded(req: req, memberIDs: memberIDs, task: task).flatMap({ task in
                                
                                return try! attachNavigatorsIfNeeded(req: req, navigatorsIDs: navigatorsIDs, task: task).flatMap({ task in
                                    
                                    return try! TasksController.find(req: req, id: task.id?.uuidString ?? "").flatMap { updatedTask in
                                        
                                        return try! updateAppointmentIfNeeded(req: req, task: updatedTask, duration: input.duration).flatMap { savedTask in
                                            
                                            if input.isArchived() {
                                                return req.eventLoop.future(savedTask)
                                            } else {
                                                return try! sendCreateTaskPush(req: req, task: updatedTask).transform(to: savedTask)
                                            }
                                        }
                                    }
                                })
                            })
                        })
                    })
                }
            }
        }
    }
    
    fileprivate func attachNavigatorsIfNeeded(req:Request, navigatorsIDs: [String], task: TaskModel ) throws -> EventLoopFuture<TaskModel> {
        if navigatorsIDs.isEmpty {
            return req.eventLoop.future(task)
        } else {
            return try! UsersController.findAll(req: req, ids: navigatorsIDs).flatMap({ allNavs in
                return try! attachNavigatorsTo(req: req, task: task, navigators: allNavs).transform(to: task)
            })
        }
    }
    
    
    fileprivate func attachMembersIfNeeded(req:Request, memberIDs: [String], task: TaskModel ) throws -> EventLoopFuture<TaskModel> {
        if memberIDs.isEmpty {
            return req.eventLoop.future(task)
        } else {
            return try! MembersController.findMembers(req: req, ids: memberIDs).flatMap({ allMembers in
                return try! attachMembersTo(req: req, task: task, members: allMembers).transform(to: task)
            })
        }
    }
    
    fileprivate func createAssigneeIfNeeded(req:Request, assigneeID: String?, task: TaskModel   ) throws -> EventLoopFuture<TaskModel> {
        if let assigneeID = assigneeID, let uuidCreator = UUID(uuidString: assigneeID) {
            task.$assignee.id = uuidCreator
            return task.update(on: req.db).transform(to: task)
        } else {
            return req.eventLoop.future(task)
        }
    }
    func addAttachment(req: Request) throws -> EventLoopFuture<TaskModel> {
        guard let id = req.parameters.get("taskID") else { throw NetworkError.error(type: .task) }
        let input = try req.content.decode(UploadInput.self)
        return try TasksController.find(req: req, id: id).flatMap { task in
            return try! AttachmentsController().uploadImageToCloudinary(req).flatMap { resposne in
                let attach = Attachment(name: input.name,
                                        kind: input.type,
                                        type: "image",
                                        url: isProfile(type: input.type) ? resposne.profileURL() : resposne.secure_url,
                                        category: input.category,
                                        refID: resposne.public_id)
                return task.$attachments.create(attach, on: req.db).transform(to: task)
            }
        }
    }
    
    func createAttachments(req: Request,
                           task: TaskModel,
                           inputs: [UploadInput]?) throws -> EventLoopFuture<Void> {
        guard let allInputs = inputs else { return req.eventLoop.future() }
        if !allInputs.isEmpty {
            return allInputs.sequencedFlatMapEach(on: req.eventLoop) { uploadInput in
                return try! AttachmentsController().uploadImageToCloudinaryWith(req,input: uploadInput).flatMap { resposne in
                    let attach = Attachment(name: uploadInput.name,
                                            kind: uploadInput.type,
                                            type: "image",
                                            url: resposne.secure_url,
                                            category: uploadInput.category,
                                            refID: resposne.public_id)
                    return task.$attachments.create(attach, on: req.db)
                }
            }
        } else {
            return req.eventLoop.future()
        }
    }
    
    fileprivate func updateAppointmentIfNeeded(req:Request, task: TaskModel, duration: String?) throws -> EventLoopFuture<TaskModel> {
        if task.isAppointmentTypeTask(), let duration = duration {
            return try AppointmentsController.findTaskAppointment(req: req, uuid: task.id).flatMap { appointment in
                if let apt = appointment {
                    apt.duration = duration
                    return apt.update(on: req.db).transform(to: task)
                } else {
                    return req.eventLoop.future(task)
                }
            }
        } else {
            return req.eventLoop.future(task)
        }
    }
    
    fileprivate func createAppointmentIfNeeded(req:Request, task: TaskModel, link: String?) throws -> EventLoopFuture<TaskModel> {
        if task.isAppointmentTypeTask(),
           let creatorID = task.assignee?.id?.uuidString.lowercased(),
           let receiver = task.receivers.last?.id?.uuidString.lowercased(),
            let orgId = task.org?.id?.uuidString.lowercased() {
            
            let input = AppointmentInput(
                creatorID: creatorID,
                title: "\(task.type) appointment",
                status: "pending",
                kind: task.remote ? "televisit" : "appointment",
                desc: "appointment scheduled by your navigator.",
                scheduleEpoc: task.dueAtEpoc,
                duration: "60",
                meetingLink: link,
                memberID: receiver,
                orgID: orgId,
                memberBooking: false, 
                taskId: task.id)
        
            return try! AppointmentsController.createAppointment(req: req, input: input).transform(to: task)
        } else {
            return req.eventLoop.future(task)
        }
    }
    
    fileprivate func createAddressIfNeeded(req:Request, input: AddressInput?, task: TaskModel) throws -> EventLoopFuture<TaskModel> {
        return try AddressService.updateAddressIfNeeded(req: req, input: input, model: task).transform(to: task)        
    }
    
    
    fileprivate func attachNavigatorsTo(req:Request, task: TaskModel, navigators: [User]) throws -> EventLoopFuture<TaskModel> {
        if navigators.isEmpty {
            return task.$completedBy.detachAll(on: req.db).transform(to: task)
        } else {
            return task.$completedBy.detachAll(on: req.db).flatMap { _ in
                return task.$completedBy.attach(navigators, on: req.db).transform(to: task)
            }
        }
    }
    
    fileprivate func attachMembersTo(req:Request, task: TaskModel, members: [Member]) throws -> EventLoopFuture<TaskModel> {
        if members.isEmpty {
            return task.$receivers.detachAll(on: req.db).transform(to: task)
        } else {
            return task.$receivers.detachAll(on: req.db).flatMap { _ in
                return task.$receivers.attach(members, on: req.db).transform(to: task)
            }
        }
    }
    
    
    //MARK: - Delete
    
    func delete(req: Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get("taskID") else { throw NetworkError.error(type: .task) }
        return try TasksController.find(req: req, id: id).flatMap { task in
            if task.isAppointmentTypeTask() {
                return try! AppointmentsController.findTaskAppointment(req: req, uuid: task.id).flatMap({ appointment in
                    if let apt = appointment {
                        return apt.delete(on: req.db).flatMap { _ in
                            return task.delete(on: req.db).transform(to: .ok)
                        }
                    } else {
                        return task.delete(on: req.db).transform(to: .ok)
                    }
                })
            } else {
                return task.delete(on: req.db).transform(to: .ok)
            }
        }
    }
    
    
    func removeCompletedByUser(req:Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get("taskID") else { throw NetworkError.error(type: .task) }
        guard let userID = req.parameters.get("userID") else { throw NetworkError.error(type: .user) }
        return try TasksController.find(req: req, id: id).flatMap { task in
            return try! UsersController.find(req: req, id: userID).flatMap { user in
                return task.$completedBy.detach(user, on: req.db).transform(to: HTTPStatus.accepted)
            }
        }
    }
    
    func removeAssignee(req:Request) throws -> EventLoopFuture<HTTPStatus> {
        guard let id = req.parameters.get("taskID") else { throw NetworkError.error(type: .task) }
        return try TasksController.find(req: req, id: id).flatMap { task in
            task.$assignee.id = nil
            return task.update(on: req.db).transform(to: HTTPStatus.accepted)
        }
    }
    
    //MARK: - PUSH
    fileprivate func sendCreateTaskPush(req: Request, task: TaskModel, creatorID: UUID? = nil) throws -> EventLoopFuture<Void> {
        guard !task.completedBy.isEmpty else { return req.eventLoop.future() }
        guard task.sendPush() else { return req.eventLoop.future() }
        guard let member = task.receivers.last else { return req.eventLoop.future() }
        guard let memberId = member.id else { return req.eventLoop.future() }
        
        let taskDesc = task.desc ?? ""
        let msg = taskDesc.isEmpty ? "New task is available." : taskDesc.capitalized
        let meta = MetaData(data: ["task" : task.id?.uuidString.lowercased() ?? ""])
        return task.completedBy.sequencedFlatMapEach(on: req.eventLoop) { user in
            
            return try! NotificationsController.notification(req: req,
                                                             input: NotificationCreateInput(title: "\(task.title) task",
                                                                                            kind: "task",
                                                                                            message: msg,
                                                                                            read: false,
                                                                                            userID: user.id?.uuidString ?? "",
                                                                                            meta: meta)).flatMap({ _ in
                return try! APNSPushController.send(req: req,
                                                    userID: user.id?.uuidString.lowercased() ?? "",
                                                    title: task.title,
                                                    subtitle: "New task is available.",
                                                    msg: task.desc ?? "You have been added to \(task.title) task.").flatMap({ _ in
                    if let creatorID {
                        return try! TimelineControllerController.create([
                            TimeLineItemMessage.general(memberId: memberId,
                                                        title: "\(task.title) task created",
                                                        desc: taskDesc,
                                                        status: "task_added",
                                                        visible: true,
                                                        meta: meta).toTimelineItem()
                        ], creatorId: creatorID, req: req)
                    } else {
                        return req.eventLoop.future()
                    }
                })
            })
        }
    }
}
