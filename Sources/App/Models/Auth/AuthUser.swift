//
//  File.swift
//  
//
//  Created by <PERSON> on 1/30/23.
//

import Foundation
import Fluent
import Vapor


struct MemberSignup: Content {
    let orgID:     String
    let username:  String
    let password:  String
    let firstName: String
    let lastName:  String
    let roles:    [String]
    
    func member(auth:String) -> Member {
        return Member(auth: auth, 
                      email: username,
                      firstName: firstName,
                      lastName: lastName,
                      type: "client",
                      roles: ["member"],
                      dob: "")
    }
}


struct UserSignup: Content {
    let orgID:     String
    let username:  String
    let password:  String
    let firstName: String
    let lastName:  String
    let roles:    [String]
    
    func user(auth:String) -> User {
        return User(email:username,
                    firstName: firstName,
                    lastName: lastName,
                    auth: auth,
                    color: profileColor(),
                    roles: roles)
    }
}

struct LoginResponse: Content {
    let token: String
    let user:  User
}

struct LoginMemberResponse: Content {
    let token: String
    let member:  Member
}

struct NewSession: Content {
    let token: String
    let user: AuthUser.Public
}

struct CreateSession: Content {
    let token: String
    let user: User
}


struct CreateMemberSession: Content {
    let token: String
    let member: Member
}

struct CreateUserSession {
    let token: String
    let user: User
}



final class AuthUser:  Model, Content, @unchecked Sendable {
    
    struct Public: Content {
        let id: UUID
        let username: String
        let createdAt: Date?
        let updatedAt: Date?
    }
    
    static let schema = "auth_users"
    
    @ID var id: UUID?
    
    @Field(key: "username")
    var username: String
    
    @Field(key: "password_hash")
    var passwordHash: String
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() {}
    
    init(id: UUID? = nil, username: String, passwordHash: String) {
        self.id = id
        self.username = username
        self.passwordHash = passwordHash
    }
}

extension AuthUser {
    static func create(from userSignup: UserSignup) throws -> AuthUser {
        AuthUser(username: userSignup.username, passwordHash: try Bcrypt.hash(userSignup.password))
    }
    
    static func create(from memberSignup: MemberSignup) throws -> AuthUser {
        AuthUser(username: memberSignup.username, passwordHash: try Bcrypt.hash(memberSignup.password))
    }
    
    static func hashPassword(from password: String) throws -> String {
        return try Bcrypt.hash(password)
    }
    
    func createToken(source: SessionSource) throws -> Token {
        let calendar = Calendar(identifier: .gregorian)
        let expiryDate = calendar.date(byAdding: .year, value: 1, to: Date())
        return try Token(userId: requireID(),
                         token: [UInt8].random(count: 16).base64, source: source, expiresAt: expiryDate)
    }
    
    func asPublic() throws -> Public {
        Public(id: try requireID(),
               username: username,
               createdAt: createdAt,
               updatedAt: updatedAt)
    }
}

extension AuthUser: ModelAuthenticatable {
    static let usernameKey = \AuthUser.$username
    static let passwordHashKey = \AuthUser.$passwordHash
    
    func verify(password: String) throws -> Bool {
        try Bcrypt.verify(password, created: self.passwordHash)
    }
}



struct CreateAuthUserMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema(AuthUser.schema)
            .id()
            .field("username",      .string,   .required).unique(on: "username")
            .field("password_hash", .string,   .required)
            .field("created_at",    .datetime, .required)
            .field("updated_at",    .datetime, .required)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema(AuthUser.schema).delete()
    }
}
