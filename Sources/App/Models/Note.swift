//
//  File.swift
//  
//
//  Created by <PERSON> on 1/29/23.
//

import Foundation
import Fluent
import Vapor

final class Note: Model, Content, @unchecked Sendable {
    static let schema = "notes"
    
    @ID var id: UUID?
    
    @OptionalParent(key: "user_id")
    public var creator: User?
    
    @Field(key: "title")
    var title: String
    
    /// member | service | household
    @Field(key: "type")
    var type: String
    
    @OptionalField(key: "subtitle")
    var subtitle: String?
    
    @Field(key: "msg")
    var msg: String            
    
    @Children(for: \.$note)
    var tags: [Tag]
    
    @OptionalParent(key: "member_id")
    var member: Member?
            
    @OptionalParent(key: "service_id")
    var service: Service?
    
    @Children(for: \.$note)
    var attachments: [Attachment]
    
    @OptionalField(key: "status")
    var status: String?
    
    @Timestamp(key: "created_at", on: .create)
    var createdAt: Date?
    
    // When this Planet was last updated.
    @Timestamp(key: "updated_at", on: .update)
    var updatedAt: Date?
    
    init() { }
    
    init(id:         UUID?    = nil,
         title:      String,
         type:       String,
         subtitle:   String?  = nil,
         msg:        String,
         status:     String? = nil
         ) {
        self.id          = id
        self.title       = title
        self.type        = type
        self.subtitle    = subtitle
        self.msg         = msg
        self.status      = status
    }
}

struct NoteMigration: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Note.schema)
            .id()
            .field("title",      .string,   .required)
            .field("type",       .string)
            .field("subtitle",   .string)
            .field("msg",        .string,   .required)
        
            .field("user_id",        .uuid, .references(User.schema,     "id", onDelete: .cascade))
            .field("member_id",      .uuid, .references(Member.schema,   "id", onDelete: .cascade))
            .field("service_id",     .uuid, .references(Service.schema,  "id", onDelete: .cascade))
        
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Note.schema).delete()
    }
}

struct NoteUpdateStatus: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return Note.query(on: database)
            .set(\.$status, to: "active")
            .update()        
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Note.schema).delete()
    }
}


struct NoteMigrationUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Note.schema)
            .field("status",  .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(Note.schema).delete()
    }
}
