import Fluent
import Vapor
import Leaf
import SotoSNS

func routes(_ app: Application) throws {
    app.get { req async in
        "Wellup"
    }
    
    app.post("sms") { req async throws -> Response in
        return try await TwilioResponseManager.handle(req: req)
    }
    
    app.post("failedSMS") { req -> EventLoopFuture<Response> in
        return try TwilioResponseManager.handleFailure(req: req)
    }
    
    app.get("index") { req async throws -> View in
        return try await req.view.render("index", ["name": "Leaf"])
    }
    
    app.get("constants") { req -> ApplicationConstants in
        return ApplicationConstants()
    }
    
    app.post("webhook") { req -> EventLoopFuture<HTTPStatus> in
        let message = try req.content.decode(WebhookMessage.self)
        return try WebhookManager.processMessage(message, on: req)
    }
    
//    app.get("kiosk") { req -> EventLoopFuture<HTTPStatus> in
//        return try CamullusDataImporter.importData(req: req).transform(to: .ok)
//    }
    
//    app.get("kiosk") { req -> EventLoopFuture<ClientResponse> in
//        return try GraphQLController.kioskLogin(req: req).flatMap { response in
//            let token  = response.login.user?.token ?? ""
//            return try! GraphQLController.cancelApt(req: req, id: "716")
////            return try! GraphQLController.fetchScheduler(token: token, req: req, id: "73")
////            return try! GraphQLController.createService(token: token, req: req)
////            return try! GraphQLController.createOrg(token: token, req: req)
////            return try! GraphQLController.fetchNextAvailableAppointment(token: token, req: req)
//        }
//    }
//    app.get("subs") { req async throws -> HTTPStatus in
//        let snsClient = SNS(client: req.aws.client, region: .useast1, timeout: .seconds(90))
//        
//        // Query all devices from the database
//        let devices = try await Device.query(on: req.db).all()
//
//        // Create SNS client
//        
//        // Subscribe each device to the new topic
//        for device in devices {
//            let subscribeInput = SNS.SubscribeInput(
//                endpoint: device.arn ?? "",          // The ARN of the device (mobile push)
//                protocol: "application",                       // Protocol type for mobile push notifications
//                topicArn: PushTopic.navigatorSubscriptionTopic // The ARN of the topic you're subscribing to
//            )
//
//            do {
//                let response = try await snsClient.subscribe(subscribeInput).get()
//                print("Successfully subscribed device \(device.deviceID) to topic with Subscription ARN: \(response.subscriptionArn ?? "N/A")")
//                device.subscriptionArn = response.subscriptionArn
//                try await device.update(on: req.db)
//            } catch {
//                print("Failed to subscribe device \(device.deviceID): \(error)")
//            }
//        }
//
//        return .ok // Return a success response
//    }
    
    app.get("orgConstants") { req -> OrgConstants in
        guard let orgID:String = req.query["org"] else {
            throw Abort(.badRequest, reason: "Missing orgID parameter")
        }
        
        guard let constants = ConstantsManager.store.first(where: { $0.orgId == orgID }) else {
            throw Abort(.notFound, reason: "Constants not found for orgID \(orgID)")
        }
        
        return constants
    }
    
    app.get("whitelabels") { req -> WhitelabelsConstants in
        return WhitelabelsConstants()
    }        

    try app.register(collection: UsersController())
    try app.register(collection: AuthController())
    try app.register(collection: AttachmentsController())
    try app.register(collection: OrgsController())
    try app.register(collection: MembersController())
    try app.register(collection: HouseholdsController())
    try app.register(collection: TeamsController())
    try app.register(collection: NetworksController())
    try app.register(collection: ServicesController())
    try app.register(collection: CarePackagesController())
    try app.register(collection: NotesController())
    
    try app.register(collection: SurveysController())
    try app.register(collection: AnswersController())
    try app.register(collection: SurveysController())
    try app.register(collection: SectionsController())
    try app.register(collection: QuestionsController())
    try app.register(collection: TemplatesController())
    
    
    try app.register(collection: TwilioController())
    try app.register(collection: ChatsController())
    try app.register(collection: APNSPushController())
    try app.register(collection: AnimalController())
    try app.register(collection: AppointmentsController())
    
    
    try app.register(collection: TasksController())
    try app.register(collection: NotificationsController())
    
    try app.register(collection: APIController())
    
    try app.register(collection: CarriersController())
    try app.register(collection: InsuranceController())
    try app.register(collection: ContentController())
    try app.register(collection: MemberChatsController())
    try app.register(collection: TimelineControllerController())
    try app.register(collection: TagsController())
    try app.register(collection: ContactController())
    
    
    try app.register(collection: AvailityController())
    try app.register(collection: NPIController())
    try app.register(collection: ScheduleController())
    
    
    
    
}
